<!DOCTYPE html>
<html>
<head>
    <title>🚨 Manual Authentication Fix</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 600px; 
            margin: 50px auto; 
            padding: 20px; 
            border: 2px solid #f44336; 
            border-radius: 8px; 
        }
        h1 { color: #f44336; text-align: center; }
        .step { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            background: #f9f9f9; 
        }
        input, button { 
            width: 100%; 
            padding: 10px; 
            margin: 5px 0; 
            border: 1px solid #ccc; 
            border-radius: 4px; 
        }
        button { 
            background: #2196F3; 
            color: white; 
            border: none; 
            cursor: pointer; 
        }
        button:hover { background: #1976D2; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        #status { 
            margin-top: 20px; 
            padding: 10px; 
            background: #f5f5f5; 
            border-radius: 4px; 
        }
    </style>
</head>
<body>
    <h1>🚨 Manual Authentication Fix</h1>
    
    <div class="step">
        <h3>Step 1: Login</h3>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password">
        <button onclick="login()">Emergency Login</button>
    </div>

    <div class="step">
        <h3>Step 2: Create Profile</h3>
        <button onclick="createProfile()">Create Missing Profile</button>
    </div>

    <div class="step">
        <h3>Step 3: Test</h3>
        <button onclick="testApp()">Go to Dashboard</button>
    </div>

    <div id="status">
        <h4>Status Messages:</h4>
        <div id="messages"></div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInWithEmailAndPassword } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore, doc, setDoc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyAJVN9dhfaRhnD686tytSg48IGI_wVtzQ8",
            authDomain: "staffmanager-9b0f2.firebaseapp.com",
            projectId: "staffmanager-9b0f2",
            storageBucket: "staffmanager-9b0f2.firebasestorage.app",
            messagingSenderId: "666727178550",
            appId: "1:666727178550:web:187e1798f2d7976b72d397"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Make functions global
        window.auth = auth;
        window.db = db;
        window.signInWithEmailAndPassword = signInWithEmailAndPassword;
        window.doc = doc;
        window.setDoc = setDoc;

        addMessage('info', 'Firebase initialized successfully');
    </script>

    <script>
        function addMessage(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `<strong>${timestamp}</strong>: ${message}`;
            document.getElementById('messages').insertBefore(div, document.getElementById('messages').firstChild);
            console.log(`🚨 Manual Fix [${type.toUpperCase()}]:`, message);
        }

        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            if (!password) {
                addMessage('error', 'Please enter password');
                return;
            }

            addMessage('info', 'Attempting login...');

            try {
                const userCredential = await window.signInWithEmailAndPassword(window.auth, email, password);
                addMessage('success', `Login successful: ${userCredential.user.email}`);
                addMessage('info', 'Now click "Create Missing Profile"');
            } catch (error) {
                addMessage('error', `Login failed: ${error.message}`);
            }
        }

        async function createProfile() {
            addMessage('info', 'Creating missing profile...');

            try {
                const user = window.auth.currentUser;
                if (!user) {
                    addMessage('error', 'No authenticated user found. Login first.');
                    return;
                }

                const userProfile = {
                    uid: user.uid,
                    email: user.email,
                    displayName: user.displayName || 'User',
                    role: 'admin',
                    businessIds: [],
                    primaryBusinessId: '',
                    createdAt: new Date(),
                    lastLoginAt: new Date()
                };

                const userDoc = window.doc(window.db, `users/${user.uid}`);
                await window.setDoc(userDoc, userProfile);

                addMessage('success', 'Profile created successfully!');
                addMessage('success', 'All features should now work');
                addMessage('info', 'Click "Go to Dashboard" to test');
            } catch (error) {
                addMessage('error', `Profile creation failed: ${error.message}`);
            }
        }

        function testApp() {
            addMessage('info', 'Navigating to dashboard...');
            window.location.href = '/dashboard';
        }
    </script>
</body>
</html>

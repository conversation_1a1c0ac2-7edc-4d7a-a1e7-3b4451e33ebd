/* You can add global styles to this file, and also import other style files */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* StaffManager Global Styles - Modern Design System */
:root {
  /* Light Theme Variables - Enhanced Color Palette */
  --sm-primary-main: #1976d2;
  --sm-primary-light: #42a5f5;
  --sm-primary-dark: #1565c0;
  --sm-secondary-main: #7c3aed;
  --sm-secondary-light: #a855f7;
  --sm-secondary-dark: #5b21b6;
  --sm-accent-main: #f59e0b;
  --sm-accent-light: #fbbf24;
  --sm-accent-dark: #d97706;

  /* Background Colors */
  --sm-background-default: #f8fafc;
  --sm-background-paper: #ffffff;
  --sm-background-surface: #f1f5f9;
  --sm-background-elevated: #ffffff;

  /* Text Colors */
  --sm-text-primary: #0f172a;
  --sm-text-secondary: #64748b;
  --sm-text-disabled: #94a3b8;
  --sm-text-hint: #cbd5e1;

  /* Border Colors */
  --sm-border-light: #e2e8f0;
  --sm-border-medium: #cbd5e1;
  --sm-border-dark: #94a3b8;

  /* Spacing System */
  --sm-spacing-xs: 4px;
  --sm-spacing-sm: 8px;
  --sm-spacing-md: 16px;
  --sm-spacing-lg: 24px;
  --sm-spacing-xl: 32px;
  --sm-spacing-2xl: 48px;
  --sm-spacing-3xl: 64px;

  /* Border Radius */
  --sm-border-radius-xs: 2px;
  --sm-border-radius-sm: 4px;
  --sm-border-radius-md: 8px;
  --sm-border-radius-lg: 12px;
  --sm-border-radius-xl: 16px;
  --sm-border-radius-2xl: 24px;
  --sm-border-radius-full: 9999px;

  /* Shadows - Enhanced with multiple levels */
  --sm-shadow-xs: 0 1px 2px rgba(0,0,0,0.05);
  --sm-shadow-sm: 0 1px 3px rgba(0,0,0,0.1), 0 1px 2px rgba(0,0,0,0.06);
  --sm-shadow-md: 0 4px 6px rgba(0,0,0,0.07), 0 2px 4px rgba(0,0,0,0.06);
  --sm-shadow-lg: 0 10px 15px rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.05);
  --sm-shadow-xl: 0 20px 25px rgba(0,0,0,0.1), 0 10px 10px rgba(0,0,0,0.04);
  --sm-shadow-2xl: 0 25px 50px rgba(0,0,0,0.25);

  /* Animation Timing */
  --sm-transition-fast: 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
  --sm-transition-normal: 300ms cubic-bezier(0.4, 0.0, 0.2, 1);
  --sm-transition-slow: 500ms cubic-bezier(0.4, 0.0, 0.2, 1);

  /* Z-Index Scale */
  --sm-z-dropdown: 1000;
  --sm-z-sticky: 1020;
  --sm-z-fixed: 1030;
  --sm-z-modal-backdrop: 1040;
  --sm-z-modal: 1050;
  --sm-z-popover: 1060;
  --sm-z-tooltip: 1070;
  --sm-z-toast: 1080;
}

/* Dark Theme Variables - Enhanced */
.dark-theme {
  --sm-primary-main: #3b82f6;
  --sm-primary-light: #60a5fa;
  --sm-primary-dark: #2563eb;
  --sm-secondary-main: #8b5cf6;
  --sm-secondary-light: #a78bfa;
  --sm-secondary-dark: #7c3aed;
  --sm-accent-main: #fbbf24;
  --sm-accent-light: #fcd34d;
  --sm-accent-dark: #f59e0b;

  /* Dark Background Colors */
  --sm-background-default: #0f172a;
  --sm-background-paper: #1e293b;
  --sm-background-surface: #334155;
  --sm-background-elevated: #475569;

  /* Dark Text Colors */
  --sm-text-primary: #f8fafc;
  --sm-text-secondary: #cbd5e1;
  --sm-text-disabled: #64748b;
  --sm-text-hint: #475569;

  /* Dark Border Colors */
  --sm-border-light: #334155;
  --sm-border-medium: #475569;
  --sm-border-dark: #64748b;

  /* Dark Shadows */
  --sm-shadow-xs: 0 1px 2px rgba(0,0,0,0.3);
  --sm-shadow-sm: 0 1px 3px rgba(0,0,0,0.4), 0 1px 2px rgba(0,0,0,0.3);
  --sm-shadow-md: 0 4px 6px rgba(0,0,0,0.4), 0 2px 4px rgba(0,0,0,0.3);
  --sm-shadow-lg: 0 10px 15px rgba(0,0,0,0.4), 0 4px 6px rgba(0,0,0,0.3);
  --sm-shadow-xl: 0 20px 25px rgba(0,0,0,0.4), 0 10px 10px rgba(0,0,0,0.2);
  --sm-shadow-2xl: 0 25px 50px rgba(0,0,0,0.6);
}

html, body {
  height: 100%;
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, "Helvetica Neue", sans-serif;
  background: var(--sm-background-default);
  color: var(--sm-text-primary);
  transition: background-color var(--sm-transition-normal), color var(--sm-transition-normal);
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* CRITICAL FIX: FORCE HAMBURGER LINES TO BE VISIBLE */
.hamburger-line,
app-sidebar .hamburger-line,
.sidebar-nav .hamburger-line {
  background-color: #ffffff !important;
  background: #ffffff !important;
  width: 100% !important;
  height: 3px !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 999 !important;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.1) !important;
}

.sidebar-nav.collapsed .hamburger-line,
.sidebar-nav.collapsed app-sidebar .hamburger-line {
  background-color: #1976d2 !important;
  background: #1976d2 !important;
  box-shadow: 0 0 2px rgba(25, 118, 210, 0.3) !important;
}

.dark-theme .hamburger-line {
  background-color: #ffffff !important;
  background: #ffffff !important;
  box-shadow: 0 0 1px rgba(255, 255, 255, 0.2) !important;
}

.dark-theme .sidebar-nav.collapsed .hamburger-line {
  background-color: #42a5f5 !important;
  background: #42a5f5 !important;
  box-shadow: 0 0 2px rgba(66, 165, 245, 0.4) !important;
}

/* CRITICAL FIX: FORCE MATERIAL ICONS TO BE VISIBLE */
.mat-icon,
mat-icon,
.material-icons {
  color: #1976d2 !important;
  opacity: 1 !important;
  visibility: visible !important;
  font-family: 'Material Icons' !important;
  font-weight: normal !important;
  font-style: normal !important;
  display: inline-block !important;
  line-height: 1 !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  word-wrap: normal !important;
  white-space: nowrap !important;
  direction: ltr !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  text-rendering: optimizeLegibility !important;
}

/* Dark theme icon colors */
.dark-theme .mat-icon,
.dark-theme mat-icon,
.dark-theme .material-icons {
  color: #42a5f5 !important;
}

/* Global Material Design Overrides */
.mat-mdc-card {
  background: var(--sm-background-paper) !important;
  color: var(--sm-text-primary) !important;
}

.mat-mdc-button {
  font-family: 'Inter', sans-serif !important;
}

.mat-mdc-form-field {
  font-family: 'Inter', sans-serif !important;
}

/* Modern Utility Classes */
/* Text Colors */
.text-primary { color: var(--sm-text-primary) !important; }
.text-secondary { color: var(--sm-text-secondary) !important; }
.text-disabled { color: var(--sm-text-disabled) !important; }
.text-hint { color: var(--sm-text-hint) !important; }

/* Background Colors */
.bg-primary { background-color: var(--sm-primary-main) !important; }
.bg-secondary { background-color: var(--sm-secondary-main) !important; }
.bg-accent { background-color: var(--sm-accent-main) !important; }
.bg-surface { background-color: var(--sm-background-surface) !important; }
.bg-paper { background-color: var(--sm-background-paper) !important; }

/* Spacing Utilities */
.p-xs { padding: var(--sm-spacing-xs) !important; }
.p-sm { padding: var(--sm-spacing-sm) !important; }
.p-md { padding: var(--sm-spacing-md) !important; }
.p-lg { padding: var(--sm-spacing-lg) !important; }
.p-xl { padding: var(--sm-spacing-xl) !important; }

.m-xs { margin: var(--sm-spacing-xs) !important; }
.m-sm { margin: var(--sm-spacing-sm) !important; }
.m-md { margin: var(--sm-spacing-md) !important; }
.m-lg { margin: var(--sm-spacing-lg) !important; }
.m-xl { margin: var(--sm-spacing-xl) !important; }

/* Border Radius */
.rounded-sm { border-radius: var(--sm-border-radius-sm) !important; }
.rounded-md { border-radius: var(--sm-border-radius-md) !important; }
.rounded-lg { border-radius: var(--sm-border-radius-lg) !important; }
.rounded-xl { border-radius: var(--sm-border-radius-xl) !important; }
.rounded-full { border-radius: var(--sm-border-radius-full) !important; }

/* Shadows */
.shadow-xs { box-shadow: var(--sm-shadow-xs) !important; }
.shadow-sm { box-shadow: var(--sm-shadow-sm) !important; }
.shadow-md { box-shadow: var(--sm-shadow-md) !important; }
.shadow-lg { box-shadow: var(--sm-shadow-lg) !important; }
.shadow-xl { box-shadow: var(--sm-shadow-xl) !important; }
.shadow-2xl { box-shadow: var(--sm-shadow-2xl) !important; }
.shadow-none { box-shadow: none !important; }

/* Transitions */
.transition-fast { transition: all var(--sm-transition-fast) !important; }
.transition-normal { transition: all var(--sm-transition-normal) !important; }
.transition-slow { transition: all var(--sm-transition-slow) !important; }

/* Flexbox Utilities */
.flex { display: flex !important; }
.flex-col { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.items-center { align-items: center !important; }
.items-start { align-items: flex-start !important; }
.items-end { align-items: flex-end !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.justify-start { justify-content: flex-start !important; }
.justify-end { justify-content: flex-end !important; }

/* Grid Utilities */
.grid { display: grid !important; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
.gap-xs { gap: var(--sm-spacing-xs) !important; }
.gap-sm { gap: var(--sm-spacing-sm) !important; }
.gap-md { gap: var(--sm-spacing-md) !important; }
.gap-lg { gap: var(--sm-spacing-lg) !important; }

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--sm-background-default);
}

::-webkit-scrollbar-thumb {
  background: var(--sm-text-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--sm-text-primary);
}
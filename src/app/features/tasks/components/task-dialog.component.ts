import { Component, Inject, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Observable, map, startWith } from 'rxjs';

// Angular Material

import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { } from '@angular/material/core';
import { ButtonModule } from 'primeng/button';

import { ChipModule } from 'primeng/chip';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { CheckboxModule } from 'primeng/checkbox';
import { MatSliderModule } from '@angular/material/slider';
import { AccordionModule } from 'primeng/accordion';
import { DividerModule } from 'primeng/divider';
import { MatSnackBar } from '@angular/material/snack-bar';

// Services
import { TaskManagementService } from '../services/task-management.service';
import { AITaskAssistantService } from '../services/ai-task-assistant.service';
import { StaffFirestoreService } from '../../staff/services/staff-firestore.service';
import { AuthService } from '../../../core/auth/auth.service';

// Models
import { Task, RecurrencePattern, CompletionRequirement } from '../models/task.model';
import { StaffMember } from '../../staff/models/staff.model';

export interface TaskDialogData {
  task?: Task;
  businessId: string;
  mode: 'create' | 'edit';
}

@Component({
  selector: 'app-task-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModuleInputTextModule,
    DropdownModule,
    CalendarModule,
    ButtonModule
    ChipModule,
    AutoCompleteModule,
    CheckboxModule,
    MatSliderModule,
    AccordionModule,
    DividerModule
  ],
  template: `
    <div class="task-dialog">
      <h2 mat-dialog-title>
        <i class="pi pi-circle"></i>
        {{ data.mode === 'create' ? 'Create New Task' : 'Edit Task' }}
      </h2>

      <mat-dialog-content>
        <form [formGroup]="taskForm" class="task-form">

          <!-- Basic Information -->
          <div class="form-section">
            <h3>Basic Information</h3>

            <div class="p-field" appearance="outline" class="full-width">
              <label>Task Title</label>
              <input pInputText formControlName="title" placeholder="Enter task title" required>
              <mat-error *ngIf="taskForm.get('title')?.hasError('required')">
                Task title is required
              </mat-error>
            </div>

            <div class="p-field" appearance="outline" class="full-width">
              <label>Description</label>
              <textarea pInputText formControlName="description" rows="3"
                       placeholder="Describe the task in detail"></textarea>
            </div>

            <div class="form-row">
              <div class="p-field" appearance="outline">
                <label>Priority</label>
                <p-dropdown formControlName="priority" required>
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                  <option value="critical">Critical</option>
                </p-dropdown>
              </div>

              <div class="p-field" appearance="outline">
                <label>Category</label>
                <p-dropdown formControlName="category" required>
                  <option value="administrative">Administrative</option>
                  <option value="customer-service">Customer Service</option>
                  <option value="training">Training</option>
                  <option value="maintenance">Maintenance</option>
                  <option value="project">Project</option>
                  <option value="safety">Safety</option>
                  <option value="compliance">Compliance</option>
                  <option value="custom">Custom</option>
                </p-dropdown>
              </div>

              <div class="p-field" appearance="outline">
                <label>Status</label>
                <p-dropdown formControlName="status" required>
                  <option value="pending">Pending</option>
                  <option value="in-progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="on-hold">On Hold</option>
                  <option value="cancelled">Cancelled</option>
                </p-dropdown>
              </div>
            </div>
          </div>

          <!-- Assignment and Scheduling -->
          <div class="form-section">
            <h3>Assignment & Scheduling</h3>

            <div class="p-field" appearance="outline" class="full-width">
              <label>Assign to Staff</label>
              <p-dropdown formControlName="assignedTo" multiple required>
                <option *ngFor="let staff of staff$ | async" [value]="staff.id">
                  {{ staff.firstName }} {{ staff.lastName }} - {{ staff.position }}
                </option>
              </p-dropdown>
              <mat-hint>Select one or more staff members</mat-hint>
            </div>

            <div class="form-row">
              <div class="p-field" appearance="outline">
                <label>Start Date</label>
                <input pInputText  formControlName="startDate">
                
                
              </div>

              <div class="p-field" appearance="outline">
                <label>Due Date</label>
                <input pInputText  formControlName="dueDate">
                
                
              </div>

              <div class="p-field" appearance="outline">
                <label>Estimated Duration (minutes)</label>
                <input pInputText type="number" formControlName="estimatedDuration"
                       placeholder="e.g., 60">
              </div>
            </div>
          </div>

          <!-- Progress and Completion -->
          <div class="form-section">
            <h3>Progress & Completion</h3>

            <div class="progress-section">
              <label>Progress: {{ taskForm.get('progress')?.value || 0 }}%</label>
              <mat-slider min="0" max="100" step="5" discrete>
                <input matSliderThumb formControlName="progress">
              </mat-slider>
            </div>

            <p-checkbox formControlName="verificationRequired">
              Require manager verification for completion
            </p-checkbox>

            <p-checkbox formControlName="syncWithCalendar">
              Sync with calendar
            </p-checkbox>
          </div>

          <!-- Advanced Options -->
          <p-accordionTab class="advanced-options">
            <p-accordionTab-header>
              <mat-panel-title>
                <i class="pi pi-cog"></i>
                Advanced Options
              </mat-panel-title>
            </mat-expansion-panel-header>

            <!-- Tags -->
            <div class="form-section">
              <div class="p-field" appearance="outline" class="full-width">
                <label>Tags</label>
                <p-chip-grid #chipGrid>
                  <p-chip-row *ngFor="let tag of tags()" (removed)="removeTag(tag)">
                    {{ tag }}
                    <button matChipRemove>
                      <i class="pi pi-times"></i>
                    </p-button>
                  </mat-chip-row>
                </mat-chip-grid>
                <input placeholder="Add tags..."
                       [matChipInputFor]="chipGrid"
                       (matChipInputTokenEnd)="addTag($event)">
              </div>
            </div>

            <!-- Recurrence -->
            <div class="form-section">
              <p-checkbox formControlName="isRecurring">
                Make this a recurring task
              </p-checkbox>

              <div *ngIf="taskForm.get('isRecurring')?.value" class="recurrence-options">
                <div class="form-row">
                  <div class="p-field" appearance="outline">
                    <label>Frequency</label>
                    <p-dropdown formControlName="recurrenceFrequency">
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                      <option value="quarterly">Quarterly</option>
                      <option value="yearly">Yearly</option>
                    </p-dropdown>
                  </div>

                  <div class="p-field" appearance="outline">
                    <label>Interval</label>
                    <input pInputText type="number" formControlName="recurrenceInterval"
                           placeholder="e.g., 2" min="1">
                    <mat-hint>Every X {{ taskForm.get('recurrenceFrequency')?.value || 'periods' }}</mat-hint>
                  </div>

                  <div class="p-field" appearance="outline">
                    <label>End Date</label>
                    <input pInputText  formControlName="recurrenceEndDate">
                    
                    
                  </div>
                </div>
              </div>
            </div>

            <!-- Location -->
            <div class="p-field" appearance="outline" class="full-width">
              <label>Location</label>
              <input pInputText formControlName="location" placeholder="Task location (optional)">
            </div>
          </p-accordionTab>

          <!-- AI Assistance -->
          <div class="ai-assistance-section" *ngIf="data.mode === 'create'">
            <p-divider></p-divider>
            <div class="ai-actions">
              <h3>
                <i class="pi pi-brain"></i>
                AI Assistance
              </h3>
              <button type="button" p-button [outlined]="true" color="primary"
                      (click)="getAISuggestions()" [disabled]="isLoadingAI()">
                <i class="pi pi-sparkles"></i>
                Get AI Suggestions
              </p-button>
            </div>
          </div>
        </form>
      </mat-dialog-content>

      <mat-dialog-actions align="end">
        <button p-button (click)="onCancel()">Cancel</p-button>
        <p-button color="primary"
                (click)="onSave()"
                [disabled]="taskForm.invalid || isSaving()">
          <i class="pi pi-circle"></i>
          {{ data.mode === 'create' ? 'Create Task' : 'Save Changes' }}
        </p-button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [`
    .task-dialog {
      width: 100%;
      max-width: 800px;

      h2 {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;

        mat-icon {
          color: var(--mdc-theme-primary);
        }
      }

      .task-form {
        .form-section {
          margin-bottom: 24px;

          h3 {
            margin: 0 0 16px 0;
            font-size: 1.1rem;
            font-weight: 500;
            color: var(--mdc-theme-on-surface);
          }

          .full-width {
            width: 100%;
          }

          .form-row {
            display: flex;
            gap: 16px;
            align-items: flex-start;

            mat-form-field {
              flex: 1;
            }
          }

          .progress-section {
            margin-bottom: 16px;

            label {
              display: block;
              margin-bottom: 8px;
              font-weight: 500;
              color: var(--mdc-theme-on-surface);
            }

            mat-slider {
              width: 100%;
            }
          }

          mat-checkbox {
            margin-bottom: 8px;
          }

          .recurrence-options {
            margin-top: 16px;
            padding: 16px;
            background: var(--mdc-theme-surface-variant);
            border-radius: 8px;
          }
        }

        .advanced-options {
          margin-bottom: 16px;

          mat-expansion-panel-header {
            mat-panel-title {
              display: flex;
              align-items: center;
              gap: 8px;

              mat-icon {
                color: var(--mdc-theme-primary);
              }
            }
          }
        }

        .ai-assistance-section {
          margin-top: 24px;
          padding-top: 16px;

          .ai-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;

            h3 {
              display: flex;
              align-items: center;
              gap: 8px;
              margin: 0;

              mat-icon {
                color: var(--mdc-theme-primary);
              }
            }
          }
        }
      }
    }

    @media (max-width: 600px) {
      .task-dialog {
        .task-form {
          .form-section {
            .form-row {
              flex-direction: column;
              gap: 8px;
            }
          }

          .ai-assistance-section {
            .ai-actions {
              flex-direction: column;
              align-items: stretch;
              gap: 16px;
            }
          }
        }
      }
    }
  `]
})
export class TaskDialogComponent implements OnInit {
  private fb = inject(FormBuilder);
  private taskService = inject(TaskManagementService);
  private aiService = inject(AITaskAssistantService);
  private staffService = inject(StaffFirestoreService);
  private authService = inject(AuthService);
  private snackBar = inject(MatSnackBar);

  // Signals
  tags = signal<string[]>([]);
  isLoadingAI = signal(false);
  isSaving = signal(false);

  // Form
  taskForm!: FormGroup;

  // Data
  staff$!: Observable<StaffMember[]>;

  constructor(
    public dialogRef: MatDialogRef<TaskDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: TaskDialogData
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadStaff();

    if (this.data.task) {
      this.populateForm(this.data.task);
    }
  }

  private initializeForm(): void {
    this.taskForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      priority: ['medium', Validators.required],
      category: ['administrative', Validators.required],
      status: ['pending', Validators.required],
      assignedTo: [[], Validators.required],
      startDate: [null],
      dueDate: [null],
      estimatedDuration: [null, [Validators.min(1)]],
      progress: [0, [Validators.min(0), Validators.max(100)]],
      verificationRequired: [false],
      syncWithCalendar: [true],
      isRecurring: [false],
      recurrenceFrequency: ['weekly'],
      recurrenceInterval: [1, [Validators.min(1)]],
      recurrenceEndDate: [null],
      location: ['']
    });
  }

  private loadStaff(): void {
    this.staff$ = this.staffService.getActiveStaff(this.data.businessId);
  }

  private populateForm(task: Task): void {
    this.taskForm.patchValue({
      title: task.title,
      description: task.description,
      priority: task.priority,
      category: task.category,
      status: task.status,
      assignedTo: task.assignedTo,
      startDate: task.startDate,
      dueDate: task.dueDate,
      estimatedDuration: task.estimatedDuration,
      progress: task.progress,
      verificationRequired: task.verificationRequired,
      syncWithCalendar: task.syncWithCalendar,
      isRecurring: task.isRecurring,
      location: task.location
    });

    if (task.tags) {
      this.tags.set([...task.tags]);
    }

    if (task.recurrencePattern) {
      this.taskForm.patchValue({
        recurrenceFrequency: task.recurrencePattern.frequency,
        recurrenceInterval: task.recurrencePattern.interval,
        recurrenceEndDate: task.recurrencePattern.endDate
      });
    }
  }

  addTag(event: any): void {
    const value = (event.value || '').trim();
    if (value && !this.tags().includes(value)) {
      this.tags.update(tags => [...tags, value]);
    }
    event.chipInput!.clear();
  }

  removeTag(tag: string): void {
    this.tags.update(tags => tags.filter(t => t !== tag));
  }

  async getAISuggestions(): Promise<void> {
    this.isLoadingAI.set(true);

    try {
      const currentUser = await this.authService.userProfile$.pipe(map(u => u)).toPromise();
      if (currentUser?.staffId) {
        const context = `Creating a ${this.taskForm.get('category')?.value} task with ${this.taskForm.get('priority')?.value} priority`;
        const suggestions = await this.aiService.generateTaskSuggestions(
          currentUser.staffId,
          this.data.businessId,
          context
        );

        if (suggestions.length > 0) {
          // Apply first suggestion to form
          const suggestion = suggestions[0];
          if (suggestion.suggestedData) {
            this.taskForm.patchValue(suggestion.suggestedData);
          }

          this.snackBar.open(`Applied AI suggestion: ${suggestion.title}`, 'Close', { duration: 5000 });
        }
      }
    } catch (error) {
      this.snackBar.open('Error getting AI suggestions', 'Close', { duration: 3000 });
      console.error('AI suggestions error:', error);
    } finally {
      this.isLoadingAI.set(false);
    }
  }

  onSave(): void {
    if (this.taskForm.valid) {
      this.isSaving.set(true);

      const formValue = this.taskForm.value;
      const currentUser = this.authService.userProfile$.pipe(map(u => u)).toPromise();

      currentUser.then(user => {
        if (!user) return;

        const taskData: Omit<Task, 'id' | 'createdAt' | 'updatedAt'> = {
          ...formValue,
          type: 'task' as const,
          businessId: this.data.businessId,
          createdBy: user.uid,
          lastModifiedBy: user.uid,
          tags: this.tags(),
          recurrencePattern: formValue.isRecurring ? {
            frequency: formValue.recurrenceFrequency,
            interval: formValue.recurrenceInterval,
            endDate: formValue.recurrenceEndDate
          } : undefined
        };

        if (this.data.mode === 'create') {
          this.taskService.createTask(taskData).subscribe({
            next: () => {
              this.snackBar.open('Task created successfully', 'Close', { duration: 3000 });
              this.dialogRef.close(true);
            },
            error: (error: any) => {
              this.snackBar.open('Error creating task', 'Close', { duration: 3000 });
              console.error('Task save error:', error);
              this.isSaving.set(false);
            }
          });
        } else {
          this.taskService.updateTask(this.data.task!.id, taskData).subscribe({
            next: () => {
              this.snackBar.open('Task updated successfully', 'Close', { duration: 3000 });
              this.dialogRef.close(true);
            },
            error: (error: any) => {
              this.snackBar.open('Error updating task', 'Close', { duration: 3000 });
              console.error('Task save error:', error);
              this.isSaving.set(false);
            }
          });
        }
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}

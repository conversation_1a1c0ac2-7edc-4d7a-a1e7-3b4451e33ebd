<div class="tasks-container">
  <!-- Header Section -->
  <div class="tasks-header">
    <div class="header-content">
      <div class="title-section">
        <h1>
          <mat-icon>assignment</mat-icon>
          Tasks & Checklists
        </h1>
        <p class="subtitle">Manage tasks, checklists, and team productivity with AI assistance</p>
      </div>

      <div class="header-actions">
        <button p-button color="primary" (click)="createTask()">
          <mat-icon>add_task</mat-icon>
          Create Task
        </button>
        <button p-button color="accent" (click)="createChecklist()">
          <mat-icon>checklist</mat-icon>
          Create Checklist
        </button>
        <button p-button [outlined]="true" color="primary" (click)="getAITaskSuggestions()">
          <mat-icon>psychology</mat-icon>
          AI Suggestions
        </button>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="filters-section">
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Search tasks and checklists</mat-label>
        <input matInput [(ngModel)]="searchText" placeholder="Search by title, description, or assignee">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Status</mat-label>
        <mat-select multiple>
          <mat-option *ngFor="let status of statusOptions" [value]="status.value">
            <p-chip [color]="status.color">{{ status.label }}</p-chip>
          </mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Priority</mat-label>
        <mat-select multiple>
          <mat-option *ngFor="let priority of priorityOptions" [value]="priority.value">
            <p-chip [color]="priority.color">{{ priority.label }}</p-chip>
          </mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Category</mat-label>
        <mat-select multiple>
          <mat-option *ngFor="let category of categoryOptions" [value]="category.value">
            {{ category.label }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <button p-button [text]="true" (click)="clearFilters()" matTooltip="Clear all filters">
        <mat-icon>clear</mat-icon>
      </button>
    </div>
  </div>

  <!-- Loading Indicator -->
  <mat-progress-bar *ngIf="isLoading()" mode="indeterminate" class="loading-bar"></mat-progress-bar>

  <!-- Main Content Tabs -->
  <mat-tab-group [(selectedIndex)]="selectedTab" class="tasks-tabs">

    <!-- Tasks Tab -->
    <mat-tab label="Tasks">
      <ng-template matTabContent>
        <div class="tab-content">

          <!-- Tasks Summary Cards -->
          <div class="summary-cards" *ngIf="tasks$ | async as tasks">
            <mat-card class="summary-card">
              <ng-template pTemplate="content">
                <div class="summary-content">
                  <mat-icon class="summary-icon pending">pending_actions</mat-icon>
                  <div class="summary-text">
                    <h3>{{ getTasksByStatus(tasks, 'pending').length }}</h3>
                    <p>Pending Tasks</p>
                  </div>
                </div>
              </ng-template>
            </mat-card>

            <mat-card class="summary-card">
              <ng-template pTemplate="content">
                <div class="summary-content">
                  <mat-icon class="summary-icon in-progress">hourglass_empty</mat-icon>
                  <div class="summary-text">
                    <h3>{{ getTasksByStatus(tasks, 'in-progress').length }}</h3>
                    <p>In Progress</p>
                  </div>
                </div>
              </ng-template>
            </mat-card>

            <mat-card class="summary-card">
              <ng-template pTemplate="content">
                <div class="summary-content">
                  <mat-icon class="summary-icon completed">check_circle</mat-icon>
                  <div class="summary-text">
                    <h3>{{ getTasksByStatus(tasks, 'completed').length }}</h3>
                    <p>Completed</p>
                  </div>
                </div>
              </ng-template>
            </mat-card>

            <mat-card class="summary-card">
              <ng-template pTemplate="content">
                <div class="summary-content">
                  <mat-icon class="summary-icon overdue">warning</mat-icon>
                  <div class="summary-text">
                    <h3>{{ getTasksByStatus(tasks, 'overdue').length }}</h3>
                    <p>Overdue</p>
                  </div>
                </div>
              </ng-template>
            </mat-card>
          </div>

          <!-- Tasks Table -->
          <mat-card class="tasks-table-card">
            <ng-template pTemplate="header">
              <ng-template pTemplate="title">
                <mat-icon>assignment</mat-icon>
                All Tasks
              </ng-template>
              <div class="table-actions">
                <button p-button [text]="true" [matMenuTriggerFor]="sortMenu" matTooltip="Sort options">
                  <mat-icon>sort</mat-icon>
                </button>
                <mat-menu #sortMenu="matMenu">
                  <button mat-menu-item (click)="applySorting({field: 'title', direction: 'asc'})">
                    <mat-icon>sort_by_alpha</mat-icon>
                    Title A-Z
                  </button>
                  <button mat-menu-item (click)="applySorting({field: 'dueDate', direction: 'asc'})">
                    <i class="pi pi-clock"></i>
                    Due Date
                  </button>
                  <button mat-menu-item (click)="applySorting({field: 'priority', direction: 'desc'})">
                    <mat-icon>priority_high</mat-icon>
                    Priority
                  </button>
                  <button mat-menu-item (click)="applySorting({field: 'createdAt', direction: 'desc'})">
                    <mat-icon>access_time</mat-icon>
                    Created Date
                  </button>
                </mat-menu>
              </div>
            </ng-template>

            <ng-template pTemplate="content">
              <div class="table-container">
                <table mat-table [dataSource]="(tasks$ | async) || []" class="tasks-table">

                  <!-- Title Column -->
                  <ng-container matColumnDef="title">
                    <th mat-header-cell *matHeaderCellDef>Task</th>
                    <td mat-cell *matCellDef="let task">
                      <div class="task-title-cell">
                        <h4>{{ task.title }}</h4>
                        <p *ngIf="task.description">{{ task.description | slice:0:100 }}{{ task.description.length > 100 ? '...' : '' }}</p>
                        <div class="task-tags" *ngIf="task.tags && task.tags.length > 0">
                          <p-chip *ngFor="let tag of task.tags | slice:0:3" class="task-tag">{{ tag }}</p-chip>
                          <span *ngIf="task.tags.length > 3" class="more-tags">+{{ task.tags.length - 3 }} more</span>
                        </div>
                      </div>
                    </td>
                  </ng-container>

                  <!-- Status Column -->
                  <ng-container matColumnDef="status">
                    <th mat-header-cell *matHeaderCellDef>Status</th>
                    <td mat-cell *matCellDef="let task">
                      <p-chip [color]="getStatusColor(task.status)">
                        {{ task.status | titlecase }}
                      </p-chip>
                    </td>
                  </ng-container>

                  <!-- Priority Column -->
                  <ng-container matColumnDef="priority">
                    <th mat-header-cell *matHeaderCellDef>Priority</th>
                    <td mat-cell *matCellDef="let task">
                      <p-chip [color]="getPriorityColor(task.priority)">
                        {{ task.priority | titlecase }}
                      </p-chip>
                    </td>
                  </ng-container>

                  <!-- Assigned To Column -->
                  <ng-container matColumnDef="assignedTo">
                    <th mat-header-cell *matHeaderCellDef>Assigned To</th>
                    <td mat-cell *matCellDef="let task">
                      <div class="assignees">
                        {{ formatStaffNames(task.assignedTo) | async }}
                      </div>
                    </td>
                  </ng-container>

                  <!-- Due Date Column -->
                  <ng-container matColumnDef="dueDate">
                    <th mat-header-cell *matHeaderCellDef>Due Date</th>
                    <td mat-cell *matCellDef="let task">
                      <div class="due-date" [class.overdue]="isTaskOverdue(task)">
                        <mat-icon *ngIf="isTaskOverdue(task)">warning</mat-icon>
                        {{ task.dueDate ? (task.dueDate | date:'mediumDate') : 'No due date' }}
                      </div>
                    </td>
                  </ng-container>

                  <!-- Progress Column -->
                  <ng-container matColumnDef="progress">
                    <th mat-header-cell *matHeaderCellDef>Progress</th>
                    <td mat-cell *matCellDef="let task">
                      <div class="progress-cell">
                        <mat-progress-bar [value]="task.progress" mode="determinate"></mat-progress-bar>
                        <span class="progress-text">{{ task.progress }}%</span>
                      </div>
                    </td>
                  </ng-container>

                  <!-- Actions Column -->
                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let task">
                      <button p-button [text]="true" [matMenuTriggerFor]="taskMenu" matTooltip="Task actions">
                        <mat-icon>more_vert</mat-icon>
                      </button>
                      <mat-menu #taskMenu="matMenu">
                        <button mat-menu-item (click)="editTask(task)">
                          <mat-icon>edit</mat-icon>
                          Edit
                        </button>
                        <button mat-menu-item>
                          <i class="pi pi-eye"></i>
                          View Details
                        </button>
                        <button mat-menu-item>
                          <mat-icon>content_copy</mat-icon>
                          Duplicate
                        </button>
                        <mat-divider></mat-divider>
                        <button mat-menu-item (click)="deleteTask(task)" class="delete-action">
                          <mat-icon>delete</mat-icon>
                          Delete
                        </button>
                      </mat-menu>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="taskColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: taskColumns;" class="task-row"></tr>
                </table>

                <!-- Empty State -->
                <div *ngIf="(tasks$ | async)?.length === 0" class="empty-state">
                  <mat-icon>assignment</mat-icon>
                  <h3>No tasks found</h3>
                  <p>Create your first task or adjust your filters to see results.</p>
                  <button p-button color="primary" (click)="createTask()">
                    <mat-icon>add</mat-icon>
                    Create Task
                  </button>
                </div>
              </div>
            </ng-template>
          </mat-card>
        </div>
      </ng-template>
    </mat-tab>

    <!-- Checklists Tab -->
    <mat-tab label="Checklists">
      <ng-template matTabContent>
        <div class="tab-content">
          <!-- Checklists content will be similar to tasks but with checklist-specific features -->
          <mat-card class="checklists-table-card">
            <ng-template pTemplate="header">
              <ng-template pTemplate="title">
                <mat-icon>checklist</mat-icon>
                All Checklists
              </ng-template>
            </ng-template>

            <ng-template pTemplate="content">
              <div class="table-container">
                <table mat-table [dataSource]="(checklists$ | async) || []" class="checklists-table">

                  <!-- Similar columns to tasks but with completion percentage -->
                  <ng-container matColumnDef="title">
                    <th mat-header-cell *matHeaderCellDef>Checklist</th>
                    <td mat-cell *matCellDef="let checklist">
                      <div class="checklist-title-cell">
                        <h4>{{ checklist.title }}</h4>
                        <p *ngIf="checklist.description">{{ checklist.description | slice:0:100 }}{{ checklist.description.length > 100 ? '...' : '' }}</p>
                        <div class="checklist-info">
                          <span class="item-count">{{ checklist.items?.length || 0 }} items</span>
                        </div>
                      </div>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="status">
                    <th mat-header-cell *matHeaderCellDef>Status</th>
                    <td mat-cell *matCellDef="let checklist">
                      <p-chip [color]="getStatusColor(checklist.status)">
                        {{ checklist.status | titlecase }}
                      </p-chip>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="priority">
                    <th mat-header-cell *matHeaderCellDef>Priority</th>
                    <td mat-cell *matCellDef="let checklist">
                      <p-chip [color]="getPriorityColor(checklist.priority)">
                        {{ checklist.priority | titlecase }}
                      </p-chip>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="assignedTo">
                    <th mat-header-cell *matHeaderCellDef>Assigned To</th>
                    <td mat-cell *matCellDef="let checklist">
                      <div class="assignees">
                        {{ formatStaffNames(checklist.assignedTo) | async }}
                      </div>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="dueDate">
                    <th mat-header-cell *matHeaderCellDef>Due Date</th>
                    <td mat-cell *matCellDef="let checklist">
                      <div class="due-date" [class.overdue]="isChecklistOverdue(checklist)">
                        <mat-icon *ngIf="isChecklistOverdue(checklist)">warning</mat-icon>
                        {{ checklist.dueDate ? (checklist.dueDate | date:'mediumDate') : 'No due date' }}
                      </div>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="completion">
                    <th mat-header-cell *matHeaderCellDef>Completion</th>
                    <td mat-cell *matCellDef="let checklist">
                      <div class="completion-cell">
                        <mat-progress-bar [value]="checklist.completionPercentage" mode="determinate"></mat-progress-bar>
                        <span class="completion-text">{{ checklist.completionPercentage }}%</span>
                      </div>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let checklist">
                      <button p-button [text]="true" [matMenuTriggerFor]="checklistMenu" matTooltip="Checklist actions">
                        <mat-icon>more_vert</mat-icon>
                      </button>
                      <mat-menu #checklistMenu="matMenu">
                        <button mat-menu-item (click)="editChecklist(checklist)">
                          <mat-icon>edit</mat-icon>
                          Edit
                        </button>
                        <button mat-menu-item>
                          <i class="pi pi-eye"></i>
                          View Details
                        </button>
                        <button mat-menu-item>
                          <mat-icon>content_copy</mat-icon>
                          Duplicate
                        </button>
                        <mat-divider></mat-divider>
                        <button mat-menu-item (click)="deleteChecklist(checklist)" class="delete-action">
                          <mat-icon>delete</mat-icon>
                          Delete
                        </button>
                      </mat-menu>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="checklistColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: checklistColumns;" class="checklist-row"></tr>
                </table>

                <!-- Empty State -->
                <div *ngIf="(checklists$ | async)?.length === 0" class="empty-state">
                  <mat-icon>checklist</mat-icon>
                  <h3>No checklists found</h3>
                  <p>Create your first checklist or adjust your filters to see results.</p>
                  <button p-button color="accent" (click)="createChecklist()">
                    <mat-icon>add</mat-icon>
                    Create Checklist
                  </button>
                </div>
              </div>
            </ng-template>
          </mat-card>
        </div>
      </ng-template>
    </mat-tab>

    <!-- Analytics Tab -->
    <mat-tab label="Analytics">
      <ng-template matTabContent>
        <div class="tab-content">
          <div class="analytics-section">
            <mat-card class="analytics-card">
              <ng-template pTemplate="header">
                <ng-template pTemplate="title">
                  <mat-icon>analytics</mat-icon>
                  Productivity Analytics
                </ng-template>
                <div class="analytics-actions">
                  <button p-button color="primary" (click)="analyzeProductivity()">
                    <mat-icon>psychology</mat-icon>
                    AI Analysis
                  </button>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <p>AI-powered productivity insights and recommendations will be displayed here.</p>
                <p>Click "AI Analysis" to generate personalized productivity recommendations.</p>
              </ng-template>
            </mat-card>
          </div>
        </div>
      </ng-template>
    </mat-tab>

  </mat-tab-group>
</div>

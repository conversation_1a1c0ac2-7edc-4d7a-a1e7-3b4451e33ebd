import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Observable, interval } from 'rxjs';
import { startWith } from 'rxjs/operators';

// Angular Material
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ChipModule } from 'primeng/chip';
import { DividerModule } from 'primeng/divider';
import { AccordionModule } from 'primeng/accordion';
import { ListboxModule } from 'primeng/listbox';
import { ToastModule } from 'primeng/toast';

// Services
import { SettingsService } from '../../../core/services/settings.service';

// Models
import { SystemStatus, ServiceStatus } from '../../../core/models/settings.model';

@Component({
  selector: 'app-systems-check',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    ButtonModule
    ProgressSpinnerModule,
    ChipModule,
    DividerModule,
    AccordionModule,
    ListboxModule,
    ToastModule
  ],
  template: `
    <div class="systems-check">
      <p-card>
        <ng-template pTemplate="header">
          <i class="pi pi-circle"></i>
          <h3>
          <p</ng-template>

        <ng-template pTemplate="content">
          <div *ngIf="systemStatus$ | async as status; else loading">
            
            <!-- Overall Status -->
            <div class="overall-status">
              <div class="status-header">
                <i class="pi pi-circle"></i>
                <h2>{{ getOverallStatusText(status.overall) }}</h2>
                <p-chip [class]="'status-' + status.overall">
                  {{ status.overall.toUpperCase() }}
                </p-chip>
              </div>
              <p class="last-checked">
                Last checked: {{ status.lastChecked | date:'medium' }}
              </p>
            </div>

            <p-divider></p-divider>

            <!-- Service Status Grid -->
            <div class="services-grid">
              <div class="service-card" *ngFor="let service of getServiceEntries(status.services)">
                <div class="service-header">
                  <i class="pi pi-circle"></i>
                  <div class="service-info">
                    <h3>{{ getServiceName(service.key) }}</h3>
                    <p-chip [class]="'status-' + service.value.status">
                      {{ service.value.status.toUpperCase() }}
                    </p-chip>
                  </div>
                </div>

                <div class="service-details" *ngIf="service.value.responseTime || service.value.uptime || service.value.version">
                  <div class="detail-item" *ngIf="service.value.responseTime">
                    <i class="pi pi-circle"></i>
                    <span>{{ service.value.responseTime }}ms</span>
                  </div>
                  <div class="detail-item" *ngIf="service.value.uptime">
                    <i class="pi pi-circle"></i>
                    <span>{{ service.value.uptime }}% uptime</span>
                  </div>
                  <div class="detail-item" *ngIf="service.value.version">
                    <i class="pi pi-info-circle"></i>
                    <span>{{ service.value.version }}</span>
                  </div>
                </div>

                <div class="service-error" *ngIf="service.value.lastError">
                  <i class="pi pi-times-circle"></i>
                  <span>{{ service.value.lastError }}</span>
                </div>
              </div>
            </div>

            <p-divider></p-divider>

            <!-- Diagnostics Section -->
            <div class="diagnostics-section">
              <h3>Diagnostics & Troubleshooting</h3>
              
              <mat-accordion>
                <!-- AI Services Diagnostics -->
                <p-accordionTab>
                  <p-accordionTab-header>
                    <mat-panel-title>
                      <i class="pi pi-brain"></i>
                      AI Services (Gemini 2.5 Flash)
                    </mat-panel-title>
                    <mat-panel-description>
                      {{ getServiceStatus(status.services.ai) }}
                    </mat-panel-description>
                  </mat-expansion-panel-header>
                  
                  <div class="diagnostic-content">
                    <p><strong>Service:</strong> Google Gemini 2.5 Flash Preview</p>
                    <p><strong>Purpose:</strong> AI-powered scheduling, insights, and automation</p>
                    <p><strong>Status:</strong> {{ status.services.ai.status }}</p>
                    
                    <div class="troubleshooting" *ngIf="status.services.ai.status !== 'online'">
                      <h4>Troubleshooting Steps:</h4>
                      <ul>
                        <li>Check your internet connection</li>
                        <li>Verify API key configuration</li>
                        <li>Check service quotas and limits</li>
                        <li>Try refreshing the page</li>
                      </ul>
                    </div>
                  </div>
                </p-accordionTab>

                <!-- Database Diagnostics -->
                <p-accordionTab>
                  <p-accordionTab-header>
                    <mat-panel-title>
                      <i class="pi pi-circle"></i>
                      Database (Firestore)
                    </mat-panel-title>
                    <mat-panel-description>
                      {{ getServiceStatus(status.services.database) }}
                    </mat-panel-description>
                  </mat-expansion-panel-header>
                  
                  <div class="diagnostic-content">
                    <p><strong>Service:</strong> Google Cloud Firestore</p>
                    <p><strong>Purpose:</strong> Data storage and real-time synchronization</p>
                    <p><strong>Status:</strong> {{ status.services.database.status }}</p>
                    
                    <div class="troubleshooting" *ngIf="status.services.database.status !== 'online'">
                      <h4>Troubleshooting Steps:</h4>
                      <ul>
                        <li>Check your internet connection</li>
                        <li>Verify Firebase project configuration</li>
                        <li>Check Firestore security rules</li>
                        <li>Verify authentication status</li>
                      </ul>
                    </div>
                  </div>
                </p-accordionTab>

                <!-- PWA Diagnostics -->
                <p-accordionTab>
                  <p-accordionTab-header>
                    <mat-panel-title>
                      <i class="pi pi-circle"></i>
                      PWA (Progressive Web App)
                    </mat-panel-title>
                    <mat-panel-description>
                      {{ getServiceStatus(status.services.pwa) }}
                    </mat-panel-description>
                  </mat-expansion-panel-header>
                  
                  <div class="diagnostic-content">
                    <p><strong>Service:</strong> Service Worker & PWA Features</p>
                    <p><strong>Purpose:</strong> Offline functionality and app-like experience</p>
                    <p><strong>Status:</strong> {{ status.services.pwa.status }}</p>
                    
                    <div class="troubleshooting" *ngIf="status.services.pwa.status !== 'online'">
                      <h4>Troubleshooting Steps:</h4>
                      <ul>
                        <li>Ensure you're using HTTPS</li>
                        <li>Clear browser cache and reload</li>
                        <li>Check if service workers are enabled</li>
                        <li>Try installing the app to home screen</li>
                      </ul>
                    </div>
                  </div>
                </p-accordionTab>

                <!-- Authentication Diagnostics -->
                <p-accordionTab>
                  <p-accordionTab-header>
                    <mat-panel-title>
                      <i class="pi pi-circle"></i>
                      Authentication
                    </mat-panel-title>
                    <mat-panel-description>
                      {{ getServiceStatus(status.services.auth) }}
                    </mat-panel-description>
                  </mat-expansion-panel-header>
                  
                  <div class="diagnostic-content">
                    <p><strong>Service:</strong> Firebase Authentication</p>
                    <p><strong>Purpose:</strong> User login and session management</p>
                    <p><strong>Status:</strong> {{ status.services.auth.status }}</p>
                    
                    <div class="troubleshooting" *ngIf="status.services.auth.status !== 'online'">
                      <h4>Troubleshooting Steps:</h4>
                      <ul>
                        <li>Try signing out and back in</li>
                        <li>Clear browser cookies</li>
                        <li>Check if third-party cookies are enabled</li>
                        <li>Verify email/password combination</li>
                      </ul>
                    </div>
                  </div>
                </p-accordionTab>

                <!-- Storage Diagnostics -->
                <p-accordionTab>
                  <p-accordionTab-header>
                    <mat-panel-title>
                      <i class="pi pi-circle"></i>
                      Local Storage
                    </mat-panel-title>
                    <mat-panel-description>
                      {{ getServiceStatus(status.services.storage) }}
                    </mat-panel-description>
                  </mat-expansion-panel-header>
                  
                  <div class="diagnostic-content">
                    <p><strong>Service:</strong> Browser Local Storage</p>
                    <p><strong>Purpose:</strong> Local data caching and preferences</p>
                    <p><strong>Status:</strong> {{ status.services.storage.status }}</p>
                    
                    <div class="troubleshooting" *ngIf="status.services.storage.status !== 'online'">
                      <h4>Troubleshooting Steps:</h4>
                      <ul>
                        <li>Check if storage is full</li>
                        <li>Clear browser data</li>
                        <li>Disable private/incognito mode</li>
                        <li>Check browser storage settings</li>
                      </ul>
                    </div>
                  </div>
                </p-accordionTab>

                <!-- Notifications Diagnostics -->
                <p-accordionTab>
                  <p-accordionTab-header>
                    <mat-panel-title>
                      <i class="pi pi-bell"></i>
                      Notifications
                    </mat-panel-title>
                    <mat-panel-description>
                      {{ getServiceStatus(status.services.notifications) }}
                    </mat-panel-description>
                  </mat-expansion-panel-header>
                  
                  <div class="diagnostic-content">
                    <p><strong>Service:</strong> Web Notifications API</p>
                    <p><strong>Purpose:</strong> Push notifications and alerts</p>
                    <p><strong>Status:</strong> {{ status.services.notifications.status }}</p>
                    
                    <div class="troubleshooting" *ngIf="status.services.notifications.status !== 'online'">
                      <h4>Troubleshooting Steps:</h4>
                      <ul>
                        <li>Enable notifications in browser settings</li>
                        <li>Check site permissions</li>
                        <li>Ensure notifications aren't blocked</li>
                        <li>Try granting permission again</li>
                      </ul>
                    </div>
                  </div>
                </p-accordionTab>
              </mat-accordion>
            </div>

          </div>

          <ng-template #loading>
            <div class="loading-state">
              <p-progressSpinner></p-progressSpinner>
              <p>Checking system status...</p>
            </div</ng-template>
        </ng-template>

        <ng-template pTemplate="footer">
          <p-button color="primary" (click)="runSystemCheck()">
            <i class="pi pi-refresh"></i>
            Run System Check
          </p-button>
          <button p-button (click)="exportDiagnostics()">
            <i class="pi pi-circle"></i>
            Export Diagnostics
          </p-button</ng-template></p-card>
    </div>
  `,
  styles: [`
    .systems-check {
      max-width: 1000px;
    }

    .overall-status {
      text-align: center;
      margin-bottom: 24px;

      .status-header {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 16px;
        margin-bottom: 8px;

        mat-icon {
          font-size: 48px;
          width: 48px;
          height: 48px;

          &.status-healthy {
            color: #4caf50;
          }

          &.status-warning {
            color: #ff9800;
          }

          &.status-error {
            color: #f44336;
          }
        }

        h2 {
          margin: 0;
          font-weight: 500;
        }

        mat-chip {
          &.status-healthy {
            background-color: #4caf50;
            color: white;
          }

          &.status-warning {
            background-color: #ff9800;
            color: white;
          }

          &.status-error {
            background-color: #f44336;
            color: white;
          }
        }
      }

      .last-checked {
        color: #666;
        margin: 0;
      }
    }

    .services-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;
      margin: 24px 0;

      .service-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;
        background-color: #fafafa;

        .service-header {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          margin-bottom: 12px;

          mat-icon {
            font-size: 32px;
            width: 32px;
            height: 32px;

            &.status-online {
              color: #4caf50;
            }

            &.status-offline {
              color: #f44336;
            }

            &.status-degraded {
              color: #ff9800;
            }

            &.status-maintenance {
              color: #9e9e9e;
            }
          }

          .service-info {
            flex: 1;

            h3 {
              margin: 0 0 8px 0;
              font-size: 1.1rem;
              font-weight: 500;
            }

            mat-chip {
              font-size: 0.75rem;
              height: 24px;

              &.status-online {
                background-color: #4caf50;
                color: white;
              }

              &.status-offline {
                background-color: #f44336;
                color: white;
              }

              &.status-degraded {
                background-color: #ff9800;
                color: white;
              }

              &.status-maintenance {
                background-color: #9e9e9e;
                color: white;
              }
            }
          }
        }

        .service-details {
          display: flex;
          flex-direction: column;
          gap: 4px;
          margin-bottom: 8px;

          .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.875rem;
            color: #666;

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }
        }

        .service-error {
          display: flex;
          align-items: flex-start;
          gap: 8px;
          padding: 8px;
          background-color: #ffebee;
          border-radius: 4px;
          font-size: 0.875rem;
          color: #c62828;

          mat-icon {
            font-size: 16px;
            width: 16px;
            height: 16px;
            margin-top: 2px;
          }
        }
      }
    }

    .diagnostics-section {
      margin-top: 24px;

      h3 {
        margin-bottom: 16px;
        color: #424242;
        font-weight: 500;
      }

      .diagnostic-content {
        p {
          margin: 8px 0;
        }

        .troubleshooting {
          margin-top: 16px;
          padding: 12px;
          background-color: #fff3e0;
          border-radius: 4px;

          h4 {
            margin: 0 0 8px 0;
            color: #e65100;
          }

          ul {
            margin: 0;
            padding-left: 20px;

            li {
              margin-bottom: 4px;
            }
          }
        }
      }

      mat-expansion-panel-header {
        mat-panel-title {
          display: flex;
          align-items: center;
          gap: 8px;

          mat-icon {
            color: #1976d2;
          }
        }
      }
    }

    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 48px;

      mat-spinner {
        margin-bottom: 16px;
      }

      p {
        color: #666;
        margin: 0;
      }
    }

    mat-card-actions {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;

      button {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }

    // Responsive design
    @media (max-width: 768px) {
      .services-grid {
        grid-template-columns: 1fr;
      }

      .overall-status {
        .status-header {
          flex-direction: column;
          gap: 8px;
        }
      }

      mat-card-actions {
        flex-direction: column;

        button {
          width: 100%;
          justify-content: center;
        }
      }
    }
  `]
})
export class SystemsCheckComponent implements OnInit {
  private settingsService = inject(SettingsService);
  private snackBar = inject(MatSnackBar);

  systemStatus$!: Observable<SystemStatus | null>;

  ngOnInit(): void {
    this.systemStatus$ = this.settingsService.systemStatus$;

    // Auto-refresh every 30 seconds
    interval(30000).pipe(
      startWith(0)
    ).subscribe(() => {
      this.settingsService.performSystemCheck();
    });
  }

  getOverallStatusIcon(status: string): string {
    switch (status) {
      case 'healthy': return 'check_circle';
      case 'warning': return 'warning';
      case 'error': return 'error';
      default: return 'help';
    }
  }

  getOverallStatusText(status: string): string {
    switch (status) {
      case 'healthy': return 'All Systems Operational';
      case 'warning': return 'Some Issues Detected';
      case 'error': return 'Critical Issues Found';
      default: return 'Unknown Status';
    }
  }

  getServiceEntries(services: any): Array<{key: string, value: ServiceStatus}> {
    if (!services) return [];
    return Object.entries(services).map(([key, value]) => ({ key, value: value as ServiceStatus }));
  }

  getServiceIcon(serviceKey: string): string {
    const icons: { [key: string]: string } = {
      ai: 'psychology',
      database: 'storage',
      pwa: 'install_mobile',
      auth: 'security',
      storage: 'folder',
      notifications: 'notifications'
    };
    return icons[serviceKey] || 'help';
  }

  getServiceName(serviceKey: string): string {
    const names: { [key: string]: string } = {
      ai: 'AI Services',
      database: 'Database',
      pwa: 'PWA',
      auth: 'Authentication',
      storage: 'Storage',
      notifications: 'Notifications'
    };
    return names[serviceKey] || serviceKey;
  }

  getServiceStatus(service: ServiceStatus): string {
    return service.status.charAt(0).toUpperCase() + service.status.slice(1);
  }

  async runSystemCheck(): Promise<void> {
    this.snackBar.open('Running system check...', 'Close', { duration: 2000 });
    await this.settingsService.performSystemCheck();
    this.snackBar.open('System check completed', 'Close', { duration: 3000 });
  }

  exportDiagnostics(): void {
    this.systemStatus$.subscribe(status => {
      if (status) {
        const diagnostics = {
          timestamp: new Date().toISOString(),
          overall: status.overall,
          services: status.services,
          lastChecked: status.lastChecked,
          userAgent: navigator.userAgent,
          url: window.location.href
        };

        const blob = new Blob([JSON.stringify(diagnostics, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `staffmanager-diagnostics-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.snackBar.open('Diagnostics exported successfully', 'Close', { duration: 3000 });
      }
    }).unsubscribe();
  }
}

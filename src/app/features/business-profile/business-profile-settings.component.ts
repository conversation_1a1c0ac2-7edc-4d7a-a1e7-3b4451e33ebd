import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Observable } from 'rxjs';

// Angular Material
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

// Services
import { BusinessProfileService } from '../../core/services/business-profile.service';

// Models
import { BusinessProfile, DayHours } from '../../core/models/business.model';

@Component({
  selector: 'app-business-profile-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatTabsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatDividerModule,
    MatSnackBarModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="business-profile-container">
      <div class="profile-header">
        <mat-card>
          <mat-card-header>
            <mat-icon mat-card-avatar>business</mat-icon>
            <mat-card-title>Business Profile Settings</mat-card-title>
            <mat-card-subtitle>Manage your business information and operational settings</mat-card-subtitle>
          </mat-card-header>
        </mat-card>
      </div>

      <div class="profile-content" *ngIf="businessProfile$ | async as profile">
        <mat-tab-group class="profile-tabs" animationDuration="300ms">

          <!-- Basic Information Tab -->
          <mat-tab label="Basic Information">
            <div class="tab-content">
              <form [formGroup]="basicInfoForm" (ngSubmit)="saveBasicInfo()">
                <mat-card>
                  <mat-card-header>
                    <mat-card-title>Business Details</mat-card-title>
                  </mat-card-header>

                  <mat-card-content>
                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Business Name</mat-label>
                        <input matInput formControlName="name" required>
                        <mat-error>Business name is required</mat-error>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Display Name</mat-label>
                        <input matInput formControlName="displayName">
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Business Type</mat-label>
                        <mat-select formControlName="businessType">
                          <mat-option value="retail">Retail</mat-option>
                          <mat-option value="restaurant">Restaurant</mat-option>
                          <mat-option value="healthcare">Healthcare</mat-option>
                          <mat-option value="professional-services">Professional Services</mat-option>
                          <mat-option value="manufacturing">Manufacturing</mat-option>
                          <mat-option value="other">Other</mat-option>
                        </mat-select>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Industry</mat-label>
                        <input matInput formControlName="industry">
                      </mat-form-field>
                    </div>

                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Description</mat-label>
                      <textarea matInput formControlName="description" rows="3"></textarea>
                    </mat-form-field>

                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Phone</mat-label>
                        <input matInput formControlName="phone" type="tel">
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Email</mat-label>
                        <input matInput formControlName="email" type="email">
                      </mat-form-field>
                    </div>

                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Website</mat-label>
                      <input matInput formControlName="website" type="url">
                    </mat-form-field>
                  </mat-card-content>

                  <mat-card-actions>
                    <button mat-raised-button color="primary" type="submit" [disabled]="basicInfoForm.invalid || saving">
                      <mat-icon>save</mat-icon>
                      Save Changes
                    </button>
                  </mat-card-actions>
                </mat-card>
              </form>
            </div>
          </mat-tab>

          <!-- Hours of Operation Tab -->
          <mat-tab label="Hours of Operation">
            <div class="tab-content">
              <form [formGroup]="hoursForm" (ngSubmit)="saveHours()">
                <mat-card>
                  <mat-card-header>
                    <mat-card-title>Customer Hours</mat-card-title>
                    <mat-card-subtitle>When your business is open to customers</mat-card-subtitle>
                  </mat-card-header>

                  <mat-card-content>
                    <div class="hours-grid">
                      <div class="day-hours" *ngFor="let day of daysOfWeek">
                        <div class="day-header">
                          <mat-slide-toggle
                            [formControlName]="day + 'IsOpen'"
                            [color]="'primary'">
                            {{ day | titlecase }}
                          </mat-slide-toggle>
                        </div>

                        <div class="time-inputs" *ngIf="hoursForm.get(day + 'IsOpen')?.value">
                          <mat-form-field appearance="outline">
                            <mat-label>Open</mat-label>
                            <input matInput type="time" [formControlName]="day + 'OpenTime'">
                          </mat-form-field>

                          <mat-form-field appearance="outline">
                            <mat-label>Close</mat-label>
                            <input matInput type="time" [formControlName]="day + 'CloseTime'">
                          </mat-form-field>
                        </div>
                      </div>
                    </div>

                    <mat-divider></mat-divider>

                    <div class="display-settings">
                      <h4>Display Settings</h4>
                      <mat-checkbox formControlName="showOnWebsite">Show hours on website</mat-checkbox>
                      <mat-checkbox formControlName="showToCustomers">Show hours to customers</mat-checkbox>
                      <mat-checkbox formControlName="autoUpdateStatus">Auto-update open/closed status</mat-checkbox>
                    </div>
                  </mat-card-content>

                  <mat-card-actions>
                    <button mat-raised-button color="primary" type="submit" [disabled]="hoursForm.invalid || saving">
                      <mat-icon>schedule</mat-icon>
                      Save Hours
                    </button>
                  </mat-card-actions>
                </mat-card>
              </form>
            </div>
          </mat-tab>

          <!-- Business Operations Tab -->
          <mat-tab label="Business Operations">
            <div class="tab-content">
              <form [formGroup]="operationsForm" (ngSubmit)="saveOperations()">
                <mat-card>
                  <mat-card-header>
                    <mat-card-title>Operational Hours</mat-card-title>
                    <mat-card-subtitle>When business operations and staff scheduling can occur</mat-card-subtitle>
                  </mat-card-header>

                  <mat-card-content>
                    <div class="operations-grid">
                      <div class="setting-group">
                        <h4>Staff Scheduling Window</h4>
                        <div class="form-row">
                          <mat-form-field appearance="outline">
                            <mat-label>Earliest Start Time</mat-label>
                            <input matInput type="time" formControlName="earliestStart">
                          </mat-form-field>

                          <mat-form-field appearance="outline">
                            <mat-label>Latest End Time</mat-label>
                            <input matInput type="time" formControlName="latestEnd">
                          </mat-form-field>
                        </div>

                        <mat-checkbox formControlName="allowOvernightShifts">
                          Allow overnight shifts
                        </mat-checkbox>
                      </div>

                      <mat-divider></mat-divider>

                      <div class="setting-group">
                        <h4>Capacity & Limits</h4>
                        <div class="form-row">
                          <mat-form-field appearance="outline">
                            <mat-label>Maximum Occupancy</mat-label>
                            <input matInput type="number" formControlName="maxOccupancy" min="1">
                          </mat-form-field>

                          <mat-form-field appearance="outline">
                            <mat-label>Max Staff on Shift</mat-label>
                            <input matInput type="number" formControlName="maxStaffOnShift" min="1">
                          </mat-form-field>
                        </div>

                        <mat-form-field appearance="outline">
                          <mat-label>Min Staff on Shift</mat-label>
                          <input matInput type="number" formControlName="minStaffOnShift" min="1">
                        </mat-form-field>
                      </div>

                      <mat-divider></mat-divider>

                      <div class="setting-group">
                        <h4>Break Policies</h4>
                        <div class="form-row">
                          <mat-form-field appearance="outline">
                            <mat-label>Break Duration (minutes)</mat-label>
                            <input matInput type="number" formControlName="breakDuration" min="5">
                          </mat-form-field>

                          <mat-form-field appearance="outline">
                            <mat-label>Meal Break Duration (minutes)</mat-label>
                            <input matInput type="number" formControlName="mealBreakDuration" min="15">
                          </mat-form-field>
                        </div>

                        <div class="form-row">
                          <mat-form-field appearance="outline">
                            <mat-label>Min Shift for Break (hours)</mat-label>
                            <input matInput type="number" formControlName="minimumShiftForBreak" min="1" step="0.5">
                          </mat-form-field>

                          <mat-form-field appearance="outline">
                            <mat-label>Min Shift for Meal (hours)</mat-label>
                            <input matInput type="number" formControlName="minimumShiftForMeal" min="1" step="0.5">
                          </mat-form-field>
                        </div>
                      </div>
                    </div>
                  </mat-card-content>

                  <mat-card-actions>
                    <button mat-raised-button color="primary" type="submit" [disabled]="operationsForm.invalid || saving">
                      <mat-icon>settings</mat-icon>
                      Save Operations
                    </button>
                  </mat-card-actions>
                </mat-card>
              </form>
            </div>
          </mat-tab>

          <!-- Staff Settings Tab -->
          <mat-tab label="Staff Settings">
            <div class="tab-content">
              <form [formGroup]="staffSettingsForm" (ngSubmit)="saveStaffSettings()">
                <mat-card>
                  <mat-card-header>
                    <mat-card-title>Staff Management</mat-card-title>
                    <mat-card-subtitle>Configure staff scheduling and time tracking policies</mat-card-subtitle>
                  </mat-card-header>

                  <mat-card-content>
                    <div class="setting-group">
                      <h4>Scheduling Preferences</h4>
                      <div class="form-row">
                        <mat-form-field appearance="outline">
                          <mat-label>Schedule Advance Notice (days)</mat-label>
                          <input matInput type="number" formControlName="scheduleAdvanceNotice" min="1">
                        </mat-form-field>
                      </div>

                      <div class="checkbox-group">
                        <mat-checkbox formControlName="allowStaffSelfScheduling">
                          Allow staff self-scheduling
                        </mat-checkbox>
                        <mat-checkbox formControlName="allowShiftSwapping">
                          Allow shift swapping between staff
                        </mat-checkbox>
                        <mat-checkbox formControlName="requireManagerApproval">
                          Require manager approval for schedule changes
                        </mat-checkbox>
                      </div>
                    </div>

                    <mat-divider></mat-divider>

                    <div class="setting-group">
                      <h4>Time Tracking</h4>
                      <mat-form-field appearance="outline">
                        <mat-label>Clock-in Method</mat-label>
                        <mat-select formControlName="clockInMethod">
                          <mat-option value="manual">Manual Entry</mat-option>
                          <mat-option value="geofenced">Geofenced (Location-based)</mat-option>
                          <mat-option value="qr-code">QR Code</mat-option>
                          <mat-option value="biometric">Biometric</mat-option>
                        </mat-select>
                      </mat-form-field>

                      <div class="checkbox-group">
                        <mat-checkbox formControlName="allowEarlyClockIn">
                          Allow early clock-in
                        </mat-checkbox>
                        <mat-checkbox formControlName="allowLateClockOut">
                          Allow late clock-out
                        </mat-checkbox>
                      </div>
                    </div>
                  </mat-card-content>

                  <mat-card-actions>
                    <button mat-raised-button color="primary" type="submit" [disabled]="staffSettingsForm.invalid || saving">
                      <mat-icon>people</mat-icon>
                      Save Staff Settings
                    </button>
                  </mat-card-actions>
                </mat-card>
              </form>
            </div>
          </mat-tab>

        </mat-tab-group>
      </div>

      <div class="loading-state" *ngIf="!(businessProfile$ | async)">
        <mat-spinner></mat-spinner>
        <p>Loading business profile...</p>
      </div>
    </div>
  `,
  styles: [`
    .business-profile-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .profile-header {
      margin-bottom: 24px;

      mat-card {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        color: white;

        mat-icon {
          color: white;
        }
      }
    }

    .profile-content {
      .profile-tabs {
        ::ng-deep .mat-mdc-tab-group {
          .mat-mdc-tab-header {
            border-bottom: 1px solid #e0e0e0;
          }
        }
      }
    }

    .tab-content {
      padding: 24px 0;

      mat-card {
        margin-bottom: 24px;

        mat-card-content {
          padding-top: 16px;
        }
      }
    }

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      mat-form-field {
        flex: 1;
      }
    }

    .full-width {
      width: 100%;
    }

    .hours-grid {
      display: grid;
      gap: 16px;

      .day-hours {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;

        .day-header {
          margin-bottom: 12px;

          mat-slide-toggle {
            font-weight: 500;
          }
        }

        .time-inputs {
          display: flex;
          gap: 12px;

          mat-form-field {
            flex: 1;
          }
        }
      }
    }

    .display-settings {
      margin-top: 24px;

      h4 {
        margin-bottom: 16px;
        color: #424242;
      }

      mat-checkbox {
        display: block;
        margin-bottom: 8px;
      }
    }

    .operations-grid {
      .setting-group {
        margin-bottom: 24px;

        h4 {
          margin-bottom: 16px;
          color: #424242;
          font-weight: 500;
        }
      }
    }

    .checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-top: 16px;

      mat-checkbox {
        margin-bottom: 8px;
      }
    }

    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 48px;

      mat-spinner {
        margin-bottom: 16px;
      }

      p {
        color: #666;
        margin: 0;
      }
    }

    // Responsive design
    @media (max-width: 768px) {
      .business-profile-container {
        padding: 16px;
      }

      .form-row {
        flex-direction: column;
        gap: 8px;
      }

      .hours-grid {
        .day-hours {
          .time-inputs {
            flex-direction: column;
            gap: 8px;
          }
        }
      }
    }
  `]
})
export class BusinessProfileSettingsComponent implements OnInit {
  private businessProfileService = inject(BusinessProfileService);
  private fb = inject(FormBuilder);
  private snackBar = inject(MatSnackBar);

  businessProfile$!: Observable<BusinessProfile | null>;

  basicInfoForm!: FormGroup;
  hoursForm!: FormGroup;
  operationsForm!: FormGroup;
  staffSettingsForm!: FormGroup;

  daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  saving = false;

  ngOnInit(): void {
    this.businessProfile$ = this.businessProfileService.selectedBusinessProfile$;
    this.initializeForms();

    // Load data when business profile changes
    this.businessProfile$.subscribe(profile => {
      if (profile) {
        this.populateForms(profile);
      }
    });
  }

  private initializeForms(): void {
    this.basicInfoForm = this.fb.group({
      name: ['', Validators.required],
      displayName: [''],
      businessType: ['retail'],
      industry: [''],
      description: [''],
      phone: [''],
      email: ['', Validators.email],
      website: ['']
    });

    this.hoursForm = this.fb.group({
      // Day toggles and times
      mondayIsOpen: [true],
      mondayOpenTime: ['09:00'],
      mondayCloseTime: ['17:00'],
      tuesdayIsOpen: [true],
      tuesdayOpenTime: ['09:00'],
      tuesdayCloseTime: ['17:00'],
      wednesdayIsOpen: [true],
      wednesdayOpenTime: ['09:00'],
      wednesdayCloseTime: ['17:00'],
      thursdayIsOpen: [true],
      thursdayOpenTime: ['09:00'],
      thursdayCloseTime: ['17:00'],
      fridayIsOpen: [true],
      fridayOpenTime: ['09:00'],
      fridayCloseTime: ['17:00'],
      saturdayIsOpen: [true],
      saturdayOpenTime: ['10:00'],
      saturdayCloseTime: ['16:00'],
      sundayIsOpen: [false],
      sundayOpenTime: [''],
      sundayCloseTime: [''],

      // Display settings
      showOnWebsite: [true],
      showToCustomers: [true],
      autoUpdateStatus: [true]
    });

    this.operationsForm = this.fb.group({
      earliestStart: ['06:00'],
      latestEnd: ['22:00'],
      allowOvernightShifts: [false],
      maxOccupancy: [null],
      maxStaffOnShift: [null],
      minStaffOnShift: [1],
      breakDuration: [15],
      mealBreakDuration: [30],
      minimumShiftForBreak: [4],
      minimumShiftForMeal: [6]
    });

    this.staffSettingsForm = this.fb.group({
      scheduleAdvanceNotice: [7],
      allowStaffSelfScheduling: [false],
      allowShiftSwapping: [true],
      requireManagerApproval: [true],
      clockInMethod: ['manual'],
      allowEarlyClockIn: [true],
      allowLateClockOut: [true]
    });
  }

  private populateForms(profile: BusinessProfile): void {
    // Populate basic info form
    this.basicInfoForm.patchValue({
      name: profile.name,
      displayName: profile.displayName,
      businessType: profile.businessType,
      industry: profile.industry,
      description: profile.description,
      phone: profile.phone,
      email: profile.email,
      website: profile.website
    });

    // Populate hours form
    const hoursData: any = {};
    this.daysOfWeek.forEach(day => {
      const dayHours = profile.hoursOfOperation[day as keyof typeof profile.hoursOfOperation] as DayHours;
      hoursData[day + 'IsOpen'] = dayHours.isOpen;
      hoursData[day + 'OpenTime'] = dayHours.openTime || '';
      hoursData[day + 'CloseTime'] = dayHours.closeTime || '';
    });

    hoursData.showOnWebsite = profile.hoursOfOperation.displaySettings.showOnWebsite;
    hoursData.showToCustomers = profile.hoursOfOperation.displaySettings.showToCustomers;
    hoursData.autoUpdateStatus = profile.hoursOfOperation.displaySettings.autoUpdateStatus;

    this.hoursForm.patchValue(hoursData);

    // Populate operations form
    this.operationsForm.patchValue({
      earliestStart: profile.hoursOfBusiness.staffSchedulingWindow.earliestStart,
      latestEnd: profile.hoursOfBusiness.staffSchedulingWindow.latestEnd,
      allowOvernightShifts: profile.hoursOfBusiness.staffSchedulingWindow.allowOvernightShifts,
      maxOccupancy: profile.operationalSettings.maxOccupancy,
      maxStaffOnShift: profile.operationalSettings.maxStaffOnShift,
      minStaffOnShift: profile.operationalSettings.minStaffOnShift,
      breakDuration: profile.operationalSettings.breakPolicies.breakDuration,
      mealBreakDuration: profile.operationalSettings.breakPolicies.mealBreakDuration,
      minimumShiftForBreak: profile.operationalSettings.breakPolicies.minimumShiftForBreak,
      minimumShiftForMeal: profile.operationalSettings.breakPolicies.minimumShiftForMeal
    });

    // Populate staff settings form
    this.staffSettingsForm.patchValue({
      scheduleAdvanceNotice: profile.staffSettings.scheduleAdvanceNotice,
      allowStaffSelfScheduling: profile.staffSettings.allowStaffSelfScheduling,
      allowShiftSwapping: profile.staffSettings.allowShiftSwapping,
      requireManagerApproval: profile.staffSettings.requireManagerApproval,
      clockInMethod: profile.staffSettings.clockInMethod,
      allowEarlyClockIn: profile.staffSettings.allowEarlyClockIn,
      allowLateClockOut: profile.staffSettings.allowLateClockOut
    });
  }

  async saveBasicInfo(): Promise<void> {
    if (this.basicInfoForm.valid) {
      this.saving = true;
      try {
        const currentProfile = await this.businessProfile$.pipe().toPromise();
        if (currentProfile) {
          await this.businessProfileService.updateBusinessProfile(currentProfile.id, {
            ...this.basicInfoForm.value
          });
          this.snackBar.open('Basic information saved successfully', 'Close', { duration: 3000 });
        }
      } catch (error) {
        this.snackBar.open('Error saving basic information', 'Close', { duration: 3000 });
      } finally {
        this.saving = false;
      }
    }
  }

  async saveHours(): Promise<void> {
    if (this.hoursForm.valid) {
      this.saving = true;
      try {
        const currentProfile = await this.businessProfile$.pipe().toPromise();
        if (currentProfile) {
          const formValue = this.hoursForm.value;
          const hoursOfOperation = { ...currentProfile.hoursOfOperation };

          // Update day hours
          this.daysOfWeek.forEach(day => {
            const dayHours: DayHours = {
              isOpen: formValue[day + 'IsOpen'],
              openTime: formValue[day + 'OpenTime'] || undefined,
              closeTime: formValue[day + 'CloseTime'] || undefined
            };
            (hoursOfOperation as any)[day] = dayHours;
          });

          // Update display settings
          hoursOfOperation.displaySettings = {
            showOnWebsite: formValue.showOnWebsite,
            showToCustomers: formValue.showToCustomers,
            autoUpdateStatus: formValue.autoUpdateStatus
          };

          await this.businessProfileService.updateBusinessProfile(currentProfile.id, {
            hoursOfOperation
          });
          this.snackBar.open('Hours of operation saved successfully', 'Close', { duration: 3000 });
        }
      } catch (error) {
        this.snackBar.open('Error saving hours of operation', 'Close', { duration: 3000 });
      } finally {
        this.saving = false;
      }
    }
  }

  async saveOperations(): Promise<void> {
    if (this.operationsForm.valid) {
      this.saving = true;
      try {
        const currentProfile = await this.businessProfile$.pipe().toPromise();
        if (currentProfile) {
          const formValue = this.operationsForm.value;

          const updates = {
            hoursOfBusiness: {
              ...currentProfile.hoursOfBusiness,
              staffSchedulingWindow: {
                earliestStart: formValue.earliestStart,
                latestEnd: formValue.latestEnd,
                allowOvernightShifts: formValue.allowOvernightShifts
              }
            },
            operationalSettings: {
              ...currentProfile.operationalSettings,
              maxOccupancy: formValue.maxOccupancy,
              maxStaffOnShift: formValue.maxStaffOnShift,
              minStaffOnShift: formValue.minStaffOnShift,
              breakPolicies: {
                ...currentProfile.operationalSettings.breakPolicies,
                breakDuration: formValue.breakDuration,
                mealBreakDuration: formValue.mealBreakDuration,
                minimumShiftForBreak: formValue.minimumShiftForBreak,
                minimumShiftForMeal: formValue.minimumShiftForMeal
              }
            }
          };

          await this.businessProfileService.updateBusinessProfile(currentProfile.id, updates);
          this.snackBar.open('Operations settings saved successfully', 'Close', { duration: 3000 });
        }
      } catch (error) {
        this.snackBar.open('Error saving operations settings', 'Close', { duration: 3000 });
      } finally {
        this.saving = false;
      }
    }
  }

  async saveStaffSettings(): Promise<void> {
    if (this.staffSettingsForm.valid) {
      this.saving = true;
      try {
        const currentProfile = await this.businessProfile$.pipe().toPromise();
        if (currentProfile) {
          const formValue = this.staffSettingsForm.value;

          const staffSettings = {
            ...currentProfile.staffSettings,
            ...formValue
          };

          await this.businessProfileService.updateBusinessProfile(currentProfile.id, {
            staffSettings
          });
          this.snackBar.open('Staff settings saved successfully', 'Close', { duration: 3000 });
        }
      } catch (error) {
        this.snackBar.open('Error saving staff settings', 'Close', { duration: 3000 });
      } finally {
        this.saving = false;
      }
    }
  }
}

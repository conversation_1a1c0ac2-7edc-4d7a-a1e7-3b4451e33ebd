import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Observable } from 'rxjs';

// Angular Material
import { TabViewModule } from 'primeng/tabview';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { DividerModule } from 'primeng/divider';
import { ToastModule } from 'primeng/toast';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

// Services
import { BusinessProfileService } from '../../core/services/business-profile.service';

// Models
import { BusinessProfile, DayHours } from '../../core/models/business.model';

@Component({
  selector: 'app-business-profile-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TabViewModule,
    CardModule,
    ButtonModule
    InputTextModule,
    DropdownModule,
    CheckboxModule,
    ToggleButtonModule,
    DividerModule,
    ToastModule,
    ProgressSpinnerModule
  ],
  template: `
    <div class="business-profile-container">
      <div class="profile-header">
        <p-card>
          <ng-template pTemplate="header">
            <i class="pi pi-building"></i>
            <h3>
            <p</ng-template></p-card>
      </div>

      <div class="profile-content" *ngIf="businessProfile$ | async as profile">
        <p-tabView class="profile-tabs" animationDuration="300ms">

          <!-- Basic Information Tab -->
          <p-tabPanel label="Basic Information">
            <div class="tab-content">
              <form [formGroup]="basicInfoForm" (ngSubmit)="saveBasicInfo()">
                <p-card>
                  <ng-template pTemplate="header"><h3</ng-template>

                  <ng-template pTemplate="content">
                    <div class="form-row">
                      <div class="p-field" appearance="outline">
                        <label>Business Name</label>
                        <input pInputText formControlName="name" required>
                        <mat-error>Business name is required</mat-error>
                      </div>

                      <div class="p-field" appearance="outline">
                        <label>Display Name</label>
                        <input pInputText formControlName="displayName">
                      </div>
                    </div>

                    <div class="form-row">
                      <div class="p-field" appearance="outline">
                        <label>Business Type</label>
                        <p-dropdown formControlName="businessType">
                          <option value="retail">Retail</option>
                          <option value="restaurant">Restaurant</option>
                          <option value="healthcare">Healthcare</option>
                          <option value="professional-services">Professional Services</option>
                          <option value="manufacturing">Manufacturing</option>
                          <option value="other">Other</option>
                        </p-dropdown>
                      </div>

                      <div class="p-field" appearance="outline">
                        <label>Industry</label>
                        <input pInputText formControlName="industry">
                      </div>
                    </div>

                    <div class="p-field" appearance="outline" class="full-width">
                      <label>Description</label>
                      <textarea pInputText formControlName="description" rows="3"></textarea>
                    </div>

                    <div class="form-row">
                      <div class="p-field" appearance="outline">
                        <label>Phone</label>
                        <input pInputText formControlName="phone" type="tel">
                      </div>

                      <div class="p-field" appearance="outline">
                        <label>Email</label>
                        <input pInputText formControlName="email" type="email">
                      </div>
                    </div>

                    <div class="p-field" appearance="outline" class="full-width">
                      <label>Website</label>
                      <input pInputText formControlName="website" type="url">
                    </div</ng-template>

                  <ng-template pTemplate="footer">
                    <p-button color="primary" type="submit" [disabled]="basicInfoForm.invalid || saving">
                      <i class="pi pi-save"></i>
                      Save Changes
                    </p-button</ng-template></p-card>
              </form>
            </div>
          </p-tabPanel>

          <!-- Hours of Operation Tab -->
          <p-tabPanel label="Hours of Operation">
            <div class="tab-content">
              <form [formGroup]="hoursForm" (ngSubmit)="saveHours()">
                <p-card>
                  <ng-template pTemplate="header"><h3>
                    <p</ng-template>

                  <ng-template pTemplate="content">
                    <div class="hours-grid">
                      <div class="day-hours" *ngFor="let day of daysOfWeek">
                        <div class="day-header">
                          <p-toggleButton
                            [formControlName]="day + 'IsOpen'"
                            [color]="'primary'">
                            {{ day | titlecase }}
                          </p-toggleButton>
                        </div>

                        <div class="time-inputs" *ngIf="hoursForm.get(day + 'IsOpen')?.value">
                          <div class="p-field" appearance="outline">
                            <label>Open</label>
                            <input pInputText type="time" [formControlName]="day + 'OpenTime'">
                          </div>

                          <div class="p-field" appearance="outline">
                            <label>Close</label>
                            <input pInputText type="time" [formControlName]="day + 'CloseTime'">
                          </div>
                        </div>
                      </div>
                    </div>

                    <p-divider></p-divider>

                    <div class="display-settings">
                      <h4>Display Settings</h4>
                      <p-checkbox formControlName="showOnWebsite">Show hours on website</p-checkbox>
                      <p-checkbox formControlName="showToCustomers">Show hours to customers</p-checkbox>
                      <p-checkbox formControlName="autoUpdateStatus">Auto-update open/closed status</p-checkbox>
                    </div</ng-template>

                  <ng-template pTemplate="footer">
                    <p-button color="primary" type="submit" [disabled]="hoursForm.invalid || saving">
                      <i class="pi pi-clock"></i>
                      Save Hours
                    </p-button</ng-template></p-card>
              </form>
            </div>
          </p-tabPanel>

          <!-- Business Operations Tab -->
          <p-tabPanel label="Business Operations">
            <div class="tab-content">
              <form [formGroup]="operationsForm" (ngSubmit)="saveOperations()">
                <p-card>
                  <ng-template pTemplate="header"><h3>
                    <p</ng-template>

                  <ng-template pTemplate="content">
                    <div class="operations-grid">
                      <div class="setting-group">
                        <h4>Staff Scheduling Window</h4>
                        <div class="form-row">
                          <div class="p-field" appearance="outline">
                            <label>Earliest Start Time</label>
                            <input pInputText type="time" formControlName="earliestStart">
                          </div>

                          <div class="p-field" appearance="outline">
                            <label>Latest End Time</label>
                            <input pInputText type="time" formControlName="latestEnd">
                          </div>
                        </div>

                        <p-checkbox formControlName="allowOvernightShifts">
                          Allow overnight shifts
                        </p-checkbox>
                      </div>

                      <p-divider></p-divider>

                      <div class="setting-group">
                        <h4>Capacity & Limits</h4>
                        <div class="form-row">
                          <div class="p-field" appearance="outline">
                            <label>Maximum Occupancy</label>
                            <input pInputText type="number" formControlName="maxOccupancy" min="1">
                          </div>

                          <div class="p-field" appearance="outline">
                            <label>Max Staff on Shift</label>
                            <input pInputText type="number" formControlName="maxStaffOnShift" min="1">
                          </div>
                        </div>

                        <div class="p-field" appearance="outline">
                          <label>Min Staff on Shift</label>
                          <input pInputText type="number" formControlName="minStaffOnShift" min="1">
                        </div>
                      </div>

                      <p-divider></p-divider>

                      <div class="setting-group">
                        <h4>Break Policies</h4>
                        <div class="form-row">
                          <div class="p-field" appearance="outline">
                            <label>Break Duration (minutes)</label>
                            <input pInputText type="number" formControlName="breakDuration" min="5">
                          </div>

                          <div class="p-field" appearance="outline">
                            <label>Meal Break Duration (minutes)</label>
                            <input pInputText type="number" formControlName="mealBreakDuration" min="15">
                          </div>
                        </div>

                        <div class="form-row">
                          <div class="p-field" appearance="outline">
                            <label>Min Shift for Break (hours)</label>
                            <input pInputText type="number" formControlName="minimumShiftForBreak" min="1" step="0.5">
                          </div>

                          <div class="p-field" appearance="outline">
                            <label>Min Shift for Meal (hours)</label>
                            <input pInputText type="number" formControlName="minimumShiftForMeal" min="1" step="0.5">
                          </div>
                        </div>
                      </div>
                    </div</ng-template>

                  <ng-template pTemplate="footer">
                    <p-button color="primary" type="submit" [disabled]="operationsForm.invalid || saving">
                      <i class="pi pi-cog"></i>
                      Save Operations
                    </p-button</ng-template></p-card>
              </form>
            </div>
          </p-tabPanel>

          <!-- Staff Settings Tab -->
          <p-tabPanel label="Staff Settings">
            <div class="tab-content">
              <form [formGroup]="staffSettingsForm" (ngSubmit)="saveStaffSettings()">
                <p-card>
                  <ng-template pTemplate="header"><h3>
                    <p</ng-template>

                  <ng-template pTemplate="content">
                    <div class="setting-group">
                      <h4>Scheduling Preferences</h4>
                      <div class="form-row">
                        <div class="p-field" appearance="outline">
                          <label>Schedule Advance Notice (days)</label>
                          <input pInputText type="number" formControlName="scheduleAdvanceNotice" min="1">
                        </div>
                      </div>

                      <div class="checkbox-group">
                        <p-checkbox formControlName="allowStaffSelfScheduling">
                          Allow staff self-scheduling
                        </p-checkbox>
                        <p-checkbox formControlName="allowShiftSwapping">
                          Allow shift swapping between staff
                        </p-checkbox>
                        <p-checkbox formControlName="requireManagerApproval">
                          Require manager approval for schedule changes
                        </p-checkbox>
                      </div>
                    </div>

                    <p-divider></p-divider>

                    <div class="setting-group">
                      <h4>Time Tracking</h4>
                      <div class="p-field" appearance="outline">
                        <label>Clock-in Method</label>
                        <p-dropdown formControlName="clockInMethod">
                          <option value="manual">Manual Entry</option>
                          <option value="geofenced">Geofenced (Location-based)</option>
                          <option value="qr-code">QR Code</option>
                          <option value="biometric">Biometric</option>
                        </p-dropdown>
                      </div>

                      <div class="checkbox-group">
                        <p-checkbox formControlName="allowEarlyClockIn">
                          Allow early clock-in
                        </p-checkbox>
                        <p-checkbox formControlName="allowLateClockOut">
                          Allow late clock-out
                        </p-checkbox>
                      </div>
                    </div</ng-template>

                  <ng-template pTemplate="footer">
                    <p-button color="primary" type="submit" [disabled]="staffSettingsForm.invalid || saving">
                      <i class="pi pi-users"></i>
                      Save Staff Settings
                    </p-button</ng-template></p-card>
              </form>
            </div>
          </p-tabPanel>

        </p-tabView>
      </div>

      <div class="loading-state" *ngIf="!(businessProfile$ | async)">
        <p-progressSpinner></p-progressSpinner>
        <p>Loading business profile...</p>
      </div>
    </div>
  `,
  styles: [`
    .business-profile-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .profile-header {
      margin-bottom: 24px;

      mat-card {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        color: white;

        mat-icon {
          color: white;
        }
      }
    }

    .profile-content {
      .profile-tabs {
        ::ng-deep .mat-mdc-tab-group {
          .mat-mdc-tab-header {
            border-bottom: 1px solid #e0e0e0;
          }
        }
      }
    }

    .tab-content {
      padding: 24px 0;

      mat-card {
        margin-bottom: 24px;

        mat-card-content {
          padding-top: 16px;
        }
      }
    }

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      mat-form-field {
        flex: 1;
      }
    }

    .full-width {
      width: 100%;
    }

    .hours-grid {
      display: grid;
      gap: 16px;

      .day-hours {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;

        .day-header {
          margin-bottom: 12px;

          mat-slide-toggle {
            font-weight: 500;
          }
        }

        .time-inputs {
          display: flex;
          gap: 12px;

          mat-form-field {
            flex: 1;
          }
        }
      }
    }

    .display-settings {
      margin-top: 24px;

      h4 {
        margin-bottom: 16px;
        color: #424242;
      }

      mat-checkbox {
        display: block;
        margin-bottom: 8px;
      }
    }

    .operations-grid {
      .setting-group {
        margin-bottom: 24px;

        h4 {
          margin-bottom: 16px;
          color: #424242;
          font-weight: 500;
        }
      }
    }

    .checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-top: 16px;

      mat-checkbox {
        margin-bottom: 8px;
      }
    }

    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 48px;

      mat-spinner {
        margin-bottom: 16px;
      }

      p {
        color: #666;
        margin: 0;
      }
    }

    // Responsive design
    @media (max-width: 768px) {
      .business-profile-container {
        padding: 16px;
      }

      .form-row {
        flex-direction: column;
        gap: 8px;
      }

      .hours-grid {
        .day-hours {
          .time-inputs {
            flex-direction: column;
            gap: 8px;
          }
        }
      }
    }
  `]
})
export class BusinessProfileSettingsComponent implements OnInit {
  private businessProfileService = inject(BusinessProfileService);
  private fb = inject(FormBuilder);
  private snackBar = inject(MatSnackBar);

  businessProfile$!: Observable<BusinessProfile | null>;

  basicInfoForm!: FormGroup;
  hoursForm!: FormGroup;
  operationsForm!: FormGroup;
  staffSettingsForm!: FormGroup;

  daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  saving = false;

  ngOnInit(): void {
    this.businessProfile$ = this.businessProfileService.selectedBusinessProfile$;
    this.initializeForms();

    // Load data when business profile changes
    this.businessProfile$.subscribe(profile => {
      if (profile) {
        this.populateForms(profile);
      }
    });
  }

  private initializeForms(): void {
    this.basicInfoForm = this.fb.group({
      name: ['', Validators.required],
      displayName: [''],
      businessType: ['retail'],
      industry: [''],
      description: [''],
      phone: [''],
      email: ['', Validators.email],
      website: ['']
    });

    this.hoursForm = this.fb.group({
      // Day toggles and times
      mondayIsOpen: [true],
      mondayOpenTime: ['09:00'],
      mondayCloseTime: ['17:00'],
      tuesdayIsOpen: [true],
      tuesdayOpenTime: ['09:00'],
      tuesdayCloseTime: ['17:00'],
      wednesdayIsOpen: [true],
      wednesdayOpenTime: ['09:00'],
      wednesdayCloseTime: ['17:00'],
      thursdayIsOpen: [true],
      thursdayOpenTime: ['09:00'],
      thursdayCloseTime: ['17:00'],
      fridayIsOpen: [true],
      fridayOpenTime: ['09:00'],
      fridayCloseTime: ['17:00'],
      saturdayIsOpen: [true],
      saturdayOpenTime: ['10:00'],
      saturdayCloseTime: ['16:00'],
      sundayIsOpen: [false],
      sundayOpenTime: [''],
      sundayCloseTime: [''],

      // Display settings
      showOnWebsite: [true],
      showToCustomers: [true],
      autoUpdateStatus: [true]
    });

    this.operationsForm = this.fb.group({
      earliestStart: ['06:00'],
      latestEnd: ['22:00'],
      allowOvernightShifts: [false],
      maxOccupancy: [null],
      maxStaffOnShift: [null],
      minStaffOnShift: [1],
      breakDuration: [15],
      mealBreakDuration: [30],
      minimumShiftForBreak: [4],
      minimumShiftForMeal: [6]
    });

    this.staffSettingsForm = this.fb.group({
      scheduleAdvanceNotice: [7],
      allowStaffSelfScheduling: [false],
      allowShiftSwapping: [true],
      requireManagerApproval: [true],
      clockInMethod: ['manual'],
      allowEarlyClockIn: [true],
      allowLateClockOut: [true]
    });
  }

  private populateForms(profile: BusinessProfile): void {
    // Populate basic info form
    this.basicInfoForm.patchValue({
      name: profile.name,
      displayName: profile.displayName,
      businessType: profile.businessType,
      industry: profile.industry,
      description: profile.description,
      phone: profile.phone,
      email: profile.email,
      website: profile.website
    });

    // Populate hours form
    const hoursData: any = {};
    this.daysOfWeek.forEach(day => {
      const dayHours = profile.hoursOfOperation[day as keyof typeof profile.hoursOfOperation] as DayHours;
      hoursData[day + 'IsOpen'] = dayHours.isOpen;
      hoursData[day + 'OpenTime'] = dayHours.openTime || '';
      hoursData[day + 'CloseTime'] = dayHours.closeTime || '';
    });

    hoursData.showOnWebsite = profile.hoursOfOperation.displaySettings.showOnWebsite;
    hoursData.showToCustomers = profile.hoursOfOperation.displaySettings.showToCustomers;
    hoursData.autoUpdateStatus = profile.hoursOfOperation.displaySettings.autoUpdateStatus;

    this.hoursForm.patchValue(hoursData);

    // Populate operations form
    this.operationsForm.patchValue({
      earliestStart: profile.hoursOfBusiness.staffSchedulingWindow.earliestStart,
      latestEnd: profile.hoursOfBusiness.staffSchedulingWindow.latestEnd,
      allowOvernightShifts: profile.hoursOfBusiness.staffSchedulingWindow.allowOvernightShifts,
      maxOccupancy: profile.operationalSettings.maxOccupancy,
      maxStaffOnShift: profile.operationalSettings.maxStaffOnShift,
      minStaffOnShift: profile.operationalSettings.minStaffOnShift,
      breakDuration: profile.operationalSettings.breakPolicies.breakDuration,
      mealBreakDuration: profile.operationalSettings.breakPolicies.mealBreakDuration,
      minimumShiftForBreak: profile.operationalSettings.breakPolicies.minimumShiftForBreak,
      minimumShiftForMeal: profile.operationalSettings.breakPolicies.minimumShiftForMeal
    });

    // Populate staff settings form
    this.staffSettingsForm.patchValue({
      scheduleAdvanceNotice: profile.staffSettings.scheduleAdvanceNotice,
      allowStaffSelfScheduling: profile.staffSettings.allowStaffSelfScheduling,
      allowShiftSwapping: profile.staffSettings.allowShiftSwapping,
      requireManagerApproval: profile.staffSettings.requireManagerApproval,
      clockInMethod: profile.staffSettings.clockInMethod,
      allowEarlyClockIn: profile.staffSettings.allowEarlyClockIn,
      allowLateClockOut: profile.staffSettings.allowLateClockOut
    });
  }

  async saveBasicInfo(): Promise<void> {
    if (this.basicInfoForm.valid) {
      this.saving = true;
      try {
        const currentProfile = await this.businessProfile$.pipe().toPromise();
        if (currentProfile) {
          await this.businessProfileService.updateBusinessProfile(currentProfile.id, {
            ...this.basicInfoForm.value
          });
          this.snackBar.open('Basic information saved successfully', 'Close', { duration: 3000 });
        }
      } catch (error) {
        this.snackBar.open('Error saving basic information', 'Close', { duration: 3000 });
      } finally {
        this.saving = false;
      }
    }
  }

  async saveHours(): Promise<void> {
    if (this.hoursForm.valid) {
      this.saving = true;
      try {
        const currentProfile = await this.businessProfile$.pipe().toPromise();
        if (currentProfile) {
          const formValue = this.hoursForm.value;
          const hoursOfOperation = { ...currentProfile.hoursOfOperation };

          // Update day hours
          this.daysOfWeek.forEach(day => {
            const dayHours: DayHours = {
              isOpen: formValue[day + 'IsOpen'],
              openTime: formValue[day + 'OpenTime'] || undefined,
              closeTime: formValue[day + 'CloseTime'] || undefined
            };
            (hoursOfOperation as any)[day] = dayHours;
          });

          // Update display settings
          hoursOfOperation.displaySettings = {
            showOnWebsite: formValue.showOnWebsite,
            showToCustomers: formValue.showToCustomers,
            autoUpdateStatus: formValue.autoUpdateStatus
          };

          await this.businessProfileService.updateBusinessProfile(currentProfile.id, {
            hoursOfOperation
          });
          this.snackBar.open('Hours of operation saved successfully', 'Close', { duration: 3000 });
        }
      } catch (error) {
        this.snackBar.open('Error saving hours of operation', 'Close', { duration: 3000 });
      } finally {
        this.saving = false;
      }
    }
  }

  async saveOperations(): Promise<void> {
    if (this.operationsForm.valid) {
      this.saving = true;
      try {
        const currentProfile = await this.businessProfile$.pipe().toPromise();
        if (currentProfile) {
          const formValue = this.operationsForm.value;

          const updates = {
            hoursOfBusiness: {
              ...currentProfile.hoursOfBusiness,
              staffSchedulingWindow: {
                earliestStart: formValue.earliestStart,
                latestEnd: formValue.latestEnd,
                allowOvernightShifts: formValue.allowOvernightShifts
              }
            },
            operationalSettings: {
              ...currentProfile.operationalSettings,
              maxOccupancy: formValue.maxOccupancy,
              maxStaffOnShift: formValue.maxStaffOnShift,
              minStaffOnShift: formValue.minStaffOnShift,
              breakPolicies: {
                ...currentProfile.operationalSettings.breakPolicies,
                breakDuration: formValue.breakDuration,
                mealBreakDuration: formValue.mealBreakDuration,
                minimumShiftForBreak: formValue.minimumShiftForBreak,
                minimumShiftForMeal: formValue.minimumShiftForMeal
              }
            }
          };

          await this.businessProfileService.updateBusinessProfile(currentProfile.id, updates);
          this.snackBar.open('Operations settings saved successfully', 'Close', { duration: 3000 });
        }
      } catch (error) {
        this.snackBar.open('Error saving operations settings', 'Close', { duration: 3000 });
      } finally {
        this.saving = false;
      }
    }
  }

  async saveStaffSettings(): Promise<void> {
    if (this.staffSettingsForm.valid) {
      this.saving = true;
      try {
        const currentProfile = await this.businessProfile$.pipe().toPromise();
        if (currentProfile) {
          const formValue = this.staffSettingsForm.value;

          const staffSettings = {
            ...currentProfile.staffSettings,
            ...formValue
          };

          await this.businessProfileService.updateBusinessProfile(currentProfile.id, {
            staffSettings
          });
          this.snackBar.open('Staff settings saved successfully', 'Close', { duration: 3000 });
        }
      } catch (error) {
        this.snackBar.open('Error saving staff settings', 'Close', { duration: 3000 });
      } finally {
        this.saving = false;
      }
    }
  }
}

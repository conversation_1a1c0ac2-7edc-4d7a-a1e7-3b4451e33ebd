import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Observable, interval } from 'rxjs';
import { map, startWith, switchMap } from 'rxjs/operators';

// Angular Material
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';

// Services
import { BusinessProfileService } from '../../core/services/business-profile.service';

// Models
import { BusinessProfile, BusinessStatus } from '../../core/models/business.model';

@Component({
  selector: 'app-business-status-widget',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatChipsModule,
    MatDividerModule
  ],
  template: `
    <mat-card class="business-status-widget">
      <mat-card-header>
        <mat-icon mat-card-avatar [class]="statusClass">
          {{ statusIcon }}
        </mat-icon>
        <mat-card-title>Business Status</mat-card-title>
        <mat-card-subtitle>{{ currentTime | date:'short' }}</mat-card-subtitle>
      </mat-card-header>

      <mat-card-content *ngIf="businessStatus$ | async as status">
        <div class="status-main">
          <div class="status-indicator">
            <mat-chip [class]="statusClass">
              <mat-icon>{{ statusIcon }}</mat-icon>
              {{ status.isCurrentlyOpen ? 'OPEN' : 'CLOSED' }}
            </mat-chip>
          </div>

          <div class="next-change" *ngIf="status.nextStatusChange">
            <span class="label">
              {{ status.isCurrentlyOpen ? 'Closes' : 'Opens' }} in:
            </span>
            <span class="time">{{ getTimeUntil(status.nextStatusChange) }}</span>
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="status-details">
          <div class="detail-item" *ngIf="status.currentShift">
            <mat-icon>schedule</mat-icon>
            <span>Current Shift: {{ status.currentShift }}</span>
          </div>

          <div class="detail-item" *ngIf="status.staffOnDuty > 0">
            <mat-icon>people</mat-icon>
            <span>{{ status.staffOnDuty }} staff on duty</span>
          </div>

          <div class="detail-item" *ngIf="status.estimatedCapacity > 0">
            <mat-icon>business</mat-icon>
            <span>Capacity: {{ status.estimatedCapacity }}</span>
          </div>
        </div>

        <div class="today-hours" *ngIf="todayHours$ | async as hours">
          <h4>Today's Hours</h4>
          <div class="hours-display" *ngIf="hours.isOpen; else closedToday">
            <mat-icon>access_time</mat-icon>
            <span>{{ hours.openTime }} - {{ hours.closeTime }}</span>
          </div>
          <ng-template #closedToday>
            <div class="hours-display closed">
              <mat-icon>block</mat-icon>
              <span>Closed Today</span>
            </div>
          </ng-template>
        </div>
      </mat-card-content>

      <mat-card-actions>
        <button mat-button routerLink="/business/profile">
          <mat-icon>settings</mat-icon>
          Manage Hours
        </button>
      </mat-card-actions>
    </mat-card>
  `,
  styles: [`
    .business-status-widget {
      height: 100%;
      display: flex;
      flex-direction: column;

      mat-card-content {
        flex: 1;
        padding: 16px;
      }
    }

    .status-main {
      text-align: center;
      margin-bottom: 16px;

      .status-indicator {
        margin-bottom: 12px;

        mat-chip {
          font-size: 16px;
          font-weight: 600;
          padding: 8px 16px;

          &.open {
            background-color: #4caf50;
            color: white;
          }

          &.closed {
            background-color: #f44336;
            color: white;
          }

          mat-icon {
            margin-right: 8px;
          }
        }
      }

      .next-change {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .label {
          font-size: 14px;
          color: #666;
        }

        .time {
          font-size: 18px;
          font-weight: 500;
          color: #1976d2;
        }
      }
    }

    .status-details {
      margin: 16px 0;

      .detail-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        font-size: 14px;

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
          color: #666;
        }
      }
    }

    .today-hours {
      h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 500;
        color: #424242;
      }

      .hours-display {
        display: flex;
        align-items: center;
        gap: 8px;

        &.closed {
          color: #f44336;
        }

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }
    }

    .open {
      color: #4caf50;
    }

    .closed {
      color: #f44336;
    }

    mat-card-avatar {
      &.open {
        background-color: #4caf50;
        color: white;
      }

      &.closed {
        background-color: #f44336;
        color: white;
      }
    }
  `]
})
export class BusinessStatusWidgetComponent implements OnInit {
  private businessProfileService = inject(BusinessProfileService);

  businessStatus$!: Observable<BusinessStatus | null>;
  todayHours$!: Observable<any>;
  currentTime = new Date();

  ngOnInit(): void {
    // Update current time every minute
    interval(60000).pipe(
      startWith(0)
    ).subscribe(() => {
      this.currentTime = new Date();
    });

    // Get business status
    this.businessStatus$ = this.businessProfileService.selectedBusinessProfile$.pipe(
      map(profile => {
        if (!profile) return null;
        return this.businessProfileService.getCurrentBusinessStatus(profile);
      })
    );

    // Get today's hours
    this.todayHours$ = this.businessProfileService.selectedBusinessProfile$.pipe(
      map(profile => {
        if (!profile) return null;

        const now = new Date();
        const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        const today = dayNames[now.getDay()];

        return profile.hoursOfOperation[today as keyof typeof profile.hoursOfOperation];
      })
    );
  }

  get statusIcon(): string {
    // This will be updated by the async pipe
    return 'store';
  }

  get statusClass(): string {
    // This will be updated by the async pipe
    return 'open';
  }

  getTimeUntil(targetDate: Date): string {
    const now = new Date();
    const diff = targetDate.getTime() - now.getTime();

    if (diff <= 0) {
      return 'Now';
    }

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }
}

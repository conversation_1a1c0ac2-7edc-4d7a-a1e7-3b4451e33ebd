import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

// Angular Material
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';

// Services
import { AuthService } from '../../core/auth/auth.service';

// Components
import { StaffAvailabilityComponent } from './components/staff-availability/staff-availability.component';
import { TimeOffManagementComponent } from './components/time-off-management/time-off-management.component';
import { SchedulingInterfaceComponent } from './components/scheduling-interface/scheduling-interface.component';
import { TimeTrackingComponent } from './components/time-tracking/time-tracking.component';
import { TimeReportsComponent } from './components/time-reports/time-reports.component';

@Component({
  selector: 'app-time-management',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatToolbarModule,
    StaffAvailabilityComponent,
    TimeOffManagementComponent,
    SchedulingInterfaceComponent,
    TimeTrackingComponent,
    TimeReportsComponent
  ],
  template: `
    <div class="time-management-container">
      <div class="time-header">
        <mat-toolbar color="primary">
          <mat-icon>schedule</mat-icon>
          <span>Time Management</span>
          <span class="spacer"></span>
        </mat-toolbar>
      </div>

      <div class="time-content">
        <mat-tab-group class="time-tabs" animationDuration="300ms">
          <!-- Staff Availability Tab -->
          <mat-tab label="Availability">
            <ng-template matTabContent>
              <div class="tab-content">
                <app-staff-availability></app-staff-availability>
              </div>
            </ng-template>
          </mat-tab>

          <!-- Time Off Requests Tab -->
          <mat-tab label="Time Off">
            <ng-template matTabContent>
              <div class="tab-content">
                <app-time-off-management></app-time-off-management>
              </div>
            </ng-template>
          </mat-tab>

          <!-- Scheduling Tab -->
          <mat-tab label="Scheduling">
            <ng-template matTabContent>
              <div class="tab-content">
                <app-scheduling-interface></app-scheduling-interface>
              </div>
            </ng-template>
          </mat-tab>

          <!-- Time Tracking Tab -->
          <mat-tab label="Time Tracking">
            <ng-template matTabContent>
              <div class="tab-content">
                <app-time-tracking></app-time-tracking>
              </div>
            </ng-template>
          </mat-tab>

          <!-- Reports Tab -->
          <mat-tab label="Reports">
            <ng-template matTabContent>
              <div class="tab-content">
                <app-time-reports></app-time-reports>
              </div>
            </ng-template>
          </mat-tab>
        </mat-tab-group>
      </div>
    </div>
  `,
  styleUrls: ['./time-management.component.scss']
})
export class TimeManagementComponent implements OnInit {
  private authService = inject(AuthService);
  private router = inject(Router);

  userProfile$ = this.authService.userProfile$;

  ngOnInit(): void {
    // Check if user has access to time management features
    this.authService.isAdminOrManager().subscribe(hasAccess => {
      if (!hasAccess) {
        // Redirect staff to their personal time view
        this.router.navigate(['/time/personal']);
      }
    });
  }
}

import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';

// Angular Material
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';

// Services
import { AIAssistantService } from '../../../../core/services/ai-assistant.service';
import { AuthService } from '../../../../core/auth/auth.service';

@Component({
  selector: 'app-scheduling-interface',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatProgressSpinnerModule,
    MatTooltipModule
  ],
  template: `
    <div class="scheduling-container">
      <div class="scheduling-header">
        <h2>
          <mat-icon>calendar_today</mat-icon>
          Schedule Management
        </h2>
        <div class="header-actions">
          <button mat-raised-button color="primary" (click)="createManualSchedule()">
            <mat-icon>add</mat-icon>
            Manual Schedule
          </button>
          <button mat-raised-button color="accent" (click)="generateAISchedule()" [disabled]="isGenerating">
            <mat-icon *ngIf="!isGenerating">psychology</mat-icon>
            <mat-spinner *ngIf="isGenerating" diameter="20"></mat-spinner>
            {{ isGenerating ? 'Generating...' : 'AI Schedule' }}
          </button>
        </div>
      </div>

      <mat-tab-group class="scheduling-tabs">
        <!-- Manual Scheduling Tab -->
        <mat-tab label="Manual Scheduling">
          <div class="tab-content">
            <div class="manual-scheduling-section">
              <mat-card class="scheduling-controls">
                <mat-card-header>
                  <mat-card-title>
                    <mat-icon>edit_calendar</mat-icon>
                    Create Schedule
                  </mat-card-title>
                  <mat-card-subtitle>Manually create and assign shifts</mat-card-subtitle>
                </mat-card-header>

                <mat-card-content>
                  <form [formGroup]="manualScheduleForm" class="schedule-form">
                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Week Starting</mat-label>
                        <input matInput [matDatepicker]="weekPicker" formControlName="weekStart">
                        <mat-datepicker-toggle matIconSuffix [for]="weekPicker"></mat-datepicker-toggle>
                        <mat-datepicker #weekPicker></mat-datepicker>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Department</mat-label>
                        <mat-select formControlName="department">
                          <mat-option value="all">All Departments</mat-option>
                          <mat-option value="sales">Sales</mat-option>
                          <mat-option value="support">Support</mat-option>
                          <mat-option value="management">Management</mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Shift Template</mat-label>
                        <mat-select formControlName="template">
                          <mat-option value="standard">Standard (9 AM - 5 PM)</mat-option>
                          <mat-option value="early">Early (7 AM - 3 PM)</mat-option>
                          <mat-option value="late">Late (1 PM - 9 PM)</mat-option>
                          <mat-option value="custom">Custom</mat-option>
                        </mat-select>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Coverage Level</mat-label>
                        <mat-select formControlName="coverage">
                          <mat-option value="minimal">Minimal</mat-option>
                          <mat-option value="standard">Standard</mat-option>
                          <mat-option value="full">Full Coverage</mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                  </form>
                </mat-card-content>

                <mat-card-actions>
                  <button mat-raised-button color="primary" (click)="buildManualSchedule()">
                    <mat-icon>build</mat-icon>
                    Build Schedule
                  </button>
                  <button mat-button (click)="resetForm()">
                    <mat-icon>refresh</mat-icon>
                    Reset
                  </button>
                </mat-card-actions>
              </mat-card>

              <!-- Schedule Grid Placeholder -->
              <mat-card class="schedule-grid-card">
                <mat-card-header>
                  <mat-card-title>Weekly Schedule Grid</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <div class="schedule-placeholder">
                    <mat-icon>calendar_view_week</mat-icon>
                    <p>Schedule grid will appear here after building a schedule</p>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- AI Scheduling Tab -->
        <mat-tab label="AI-Powered Scheduling">
          <div class="tab-content">
            <div class="ai-scheduling-section">
              <mat-card class="ai-controls">
                <mat-card-header>
                  <mat-card-title>
                    <mat-icon>psychology</mat-icon>
                    AI Schedule Generator
                  </mat-card-title>
                  <mat-card-subtitle>Powered by Gemini 2.5 Flash - Intelligent scheduling with optimization</mat-card-subtitle>
                </mat-card-header>

                <mat-card-content>
                  <form [formGroup]="aiScheduleForm" class="ai-form">
                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Schedule Period</mat-label>
                        <mat-select formControlName="period">
                          <mat-option value="week">1 Week</mat-option>
                          <mat-option value="biweek">2 Weeks</mat-option>
                          <mat-option value="month">1 Month</mat-option>
                        </mat-select>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Optimization Priority</mat-label>
                        <mat-select formControlName="priority">
                          <mat-option value="cost">Cost Optimization</mat-option>
                          <mat-option value="coverage">Maximum Coverage</mat-option>
                          <mat-option value="balance">Work-Life Balance</mat-option>
                          <mat-option value="skills">Skill Matching</mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>

                    <div class="ai-features">
                      <h4>AI Features Enabled:</h4>
                      <div class="feature-list">
                        <div class="feature-item">
                          <mat-icon>check_circle</mat-icon>
                          <span>Staff availability analysis</span>
                        </div>
                        <div class="feature-item">
                          <mat-icon>check_circle</mat-icon>
                          <span>Time off integration</span>
                        </div>
                        <div class="feature-item">
                          <mat-icon>check_circle</mat-icon>
                          <span>Hours of operation compliance</span>
                        </div>
                        <div class="feature-item">
                          <mat-icon>check_circle</mat-icon>
                          <span>Workload balancing</span>
                        </div>
                        <div class="feature-item">
                          <mat-icon>check_circle</mat-icon>
                          <span>Skill-based assignments</span>
                        </div>
                      </div>
                    </div>
                  </form>
                </mat-card-content>

                <mat-card-actions>
                  <button mat-raised-button color="accent" (click)="generateAISchedule()" [disabled]="isGenerating">
                    <mat-icon *ngIf="!isGenerating">auto_awesome</mat-icon>
                    <mat-spinner *ngIf="isGenerating" diameter="20"></mat-spinner>
                    {{ isGenerating ? 'Generating AI Schedule...' : 'Generate AI Schedule' }}
                  </button>
                  <button mat-button (click)="previewAISchedule()" [disabled]="isGenerating">
                    <mat-icon>preview</mat-icon>
                    Preview
                  </button>
                </mat-card-actions>
              </mat-card>

              <!-- AI Schedule Results -->
              <mat-card class="ai-results" *ngIf="aiScheduleResults">
                <mat-card-header>
                  <mat-card-title>
                    <mat-icon>auto_awesome</mat-icon>
                    AI Generated Schedule
                  </mat-card-title>
                  <mat-card-subtitle>Optimized schedule with {{ aiScheduleResults.confidence }}% confidence</mat-card-subtitle>
                </mat-card-header>

                <mat-card-content>
                  <div class="ai-insights">
                    <h4>AI Insights:</h4>
                    <div [innerHTML]="aiScheduleResults.insights"></div>
                  </div>

                  <div class="schedule-preview">
                    <p>Schedule preview will be displayed here</p>
                  </div>
                </mat-card-content>

                <mat-card-actions>
                  <button mat-raised-button color="primary" (click)="applyAISchedule()">
                    <mat-icon>check</mat-icon>
                    Apply Schedule
                  </button>
                  <button mat-button (click)="modifyAISchedule()">
                    <mat-icon>edit</mat-icon>
                    Modify
                  </button>
                  <button mat-button (click)="regenerateAISchedule()">
                    <mat-icon>refresh</mat-icon>
                    Regenerate
                  </button>
                </mat-card-actions>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Schedule Templates Tab -->
        <mat-tab label="Templates">
          <div class="tab-content">
            <div class="templates-section">
              <mat-card class="templates-card">
                <mat-card-header>
                  <mat-card-title>
                    <mat-icon>library_books</mat-icon>
                    Schedule Templates
                  </mat-card-title>
                  <mat-card-subtitle>Save and reuse common scheduling patterns</mat-card-subtitle>
                </mat-card-header>

                <mat-card-content>
                  <div class="templates-placeholder">
                    <mat-icon>schedule</mat-icon>
                    <p>Schedule templates will be available here</p>
                    <button mat-raised-button color="primary">
                      <mat-icon>add</mat-icon>
                      Create Template
                    </button>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  `,
  styleUrls: ['./scheduling-interface.component.scss']
})
export class SchedulingInterfaceComponent implements OnInit {
  private aiService = inject(AIAssistantService);
  private authService = inject(AuthService);
  private fb = inject(FormBuilder);

  // Forms
  manualScheduleForm: FormGroup;
  aiScheduleForm: FormGroup;

  // State
  isGenerating = false;
  aiScheduleResults: any = null;

  constructor() {
    this.manualScheduleForm = this.fb.group({
      weekStart: [new Date()],
      department: ['all'],
      template: ['standard'],
      coverage: ['standard']
    });

    this.aiScheduleForm = this.fb.group({
      period: ['week'],
      priority: ['balance']
    });
  }

  ngOnInit(): void {
    // Initialize component
  }

  createManualSchedule(): void {
    console.log('Create manual schedule');
  }

  buildManualSchedule(): void {
    console.log('Build manual schedule with:', this.manualScheduleForm.value);
  }

  resetForm(): void {
    this.manualScheduleForm.reset();
  }

  async generateAISchedule(): Promise<void> {
    this.isGenerating = true;
    
    try {
      // TODO: Implement AI schedule generation using Gemini 2.5 Flash
      await new Promise(resolve => setTimeout(resolve, 3000)); // Simulate API call
      
      this.aiScheduleResults = {
        confidence: 92,
        insights: `
          <p><strong>Optimization Results:</strong></p>
          <ul>
            <li>Achieved 95% coverage with optimal staff distribution</li>
            <li>Balanced workload across all team members</li>
            <li>Respected all availability constraints and time off requests</li>
            <li>Minimized overtime costs while maintaining service levels</li>
          </ul>
        `
      };
    } catch (error) {
      console.error('AI schedule generation failed:', error);
    } finally {
      this.isGenerating = false;
    }
  }

  previewAISchedule(): void {
    console.log('Preview AI schedule');
  }

  applyAISchedule(): void {
    console.log('Apply AI schedule');
  }

  modifyAISchedule(): void {
    console.log('Modify AI schedule');
  }

  regenerateAISchedule(): void {
    this.aiScheduleResults = null;
    this.generateAISchedule();
  }
}

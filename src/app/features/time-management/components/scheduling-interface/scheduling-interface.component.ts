import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';

// Angular Material
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

import { TabViewModule } from 'primeng/tabview';

import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { MatNativeDateModule } from '@angular/material/core';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { TooltipModule } from 'primeng/tooltip';

// Services
import { AIAssistantService } from '../../../../core/services/ai-assistant.service';
import { AuthService } from '../../../../core/auth/auth.service';

@Component({
  selector: 'app-scheduling-interface',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CardModule,
    ButtonModule
    TabViewModule
    DropdownModule,
    CalendarModule,
    MatNativeDateModule,
    ProgressSpinnerModule,
    TooltipModule
  ],
  template: `
    <div class="scheduling-container">
      <div class="scheduling-header">
        <h2>
          <mat-icon>calendar_today</mat-icon>
          Schedule Management
        </h2>
        <div class="header-actions">
          <button p-button color="primary" (click)="createManualSchedule()">
            <i class="pi pi-plus"></i>
            Manual Schedule
          </button>
          <button p-button color="accent" (click)="generateAISchedule()" [disabled]="isGenerating">
            <mat-icon *ngIf="!isGenerating">psychology</mat-icon>
            <mat-spinner *ngIf="isGenerating" diameter="20"></mat-spinner>
            {{ isGenerating ? 'Generating...' : 'AI Schedule' }}
          </button>
        </div>
      </div>

      <p-tabView class="scheduling-tabs">
        <!-- Manual Scheduling Tab -->
        <p-tabPanel label="Manual Scheduling">
          <div class="tab-content">
            <div class="manual-scheduling-section">
              <p-card class="scheduling-controls">
                <p-card-header>
                  <p-card-title>
                    <mat-icon>edit_calendar</mat-icon>
                    Create Schedule
                  </ng-template>
                  <p-card-subtitle>Manually create and assign shifts</ng-template>
                </ng-template>

                <p-card-content>
                  <form [formGroup]="manualScheduleForm" class="schedule-form">
                    <div class="form-row">
                      <div class="p-field" appearance="outline">
                        <label>Week Starting</label>
                        <input matInput [matDatepicker]="weekPicker" formControlName="weekStart">
                        <mat-datepicker-toggle matIconSuffix [for]="weekPicker"></mat-datepicker-toggle>
                        <mat-datepicker #weekPicker></mat-datepicker>
                      </div>

                      <div class="p-field" appearance="outline">
                        <label>Department</label>
                        <p-dropdown formControlName="department">
                          <p-option value="all">All Departments</p-option>
                          <p-option value="sales">Sales</p-option>
                          <p-option value="support">Support</p-option>
                          <p-option value="management">Management</p-option>
                        </p-dropdown>
                      </div>
                    </div>

                    <div class="form-row">
                      <div class="p-field" appearance="outline">
                        <label>Shift Template</label>
                        <p-dropdown formControlName="template">
                          <p-option value="standard">Standard (9 AM - 5 PM)</p-option>
                          <p-option value="early">Early (7 AM - 3 PM)</p-option>
                          <p-option value="late">Late (1 PM - 9 PM)</p-option>
                          <p-option value="custom">Custom</p-option>
                        </p-dropdown>
                      </div>

                      <div class="p-field" appearance="outline">
                        <label>Coverage Level</label>
                        <p-dropdown formControlName="coverage">
                          <p-option value="minimal">Minimal</p-option>
                          <p-option value="standard">Standard</p-option>
                          <p-option value="full">Full Coverage</p-option>
                        </p-dropdown>
                      </div>
                    </div>
                  </form>
                </ng-template>

                <p-card-actions>
                  <button p-button color="primary" (click)="buildManualSchedule()">
                    <mat-icon>build</mat-icon>
                    Build Schedule
                  </button>
                  <button mat-button (click)="resetForm()">
                    <mat-icon>refresh</mat-icon>
                    Reset
                  </button>
                </ng-template>
              </p-card>

              <!-- Schedule Grid Placeholder -->
              <p-card class="schedule-grid-card">
                <p-card-header>
                  <p-card-title>Weekly Schedule Grid</ng-template>
                </ng-template>
                <p-card-content>
                  <div class="schedule-placeholder">
                    <mat-icon>calendar_view_week</mat-icon>
                    <p>Schedule grid will appear here after building a schedule</p>
                  </div>
                </ng-template>
              </p-card>
            </div>
          </div>
        </p-tabPanel>

        <!-- AI Scheduling Tab -->
        <p-tabPanel label="AI-Powered Scheduling">
          <div class="tab-content">
            <div class="ai-scheduling-section">
              <p-card class="ai-controls">
                <p-card-header>
                  <p-card-title>
                    <mat-icon>psychology</mat-icon>
                    AI Schedule Generator
                  </ng-template>
                  <p-card-subtitle>Powered by Gemini 2.5 Flash - Intelligent scheduling with optimization</ng-template>
                </ng-template>

                <p-card-content>
                  <form [formGroup]="aiScheduleForm" class="ai-form">
                    <div class="form-row">
                      <div class="p-field" appearance="outline">
                        <label>Schedule Period</label>
                        <p-dropdown formControlName="period">
                          <p-option value="week">1 Week</p-option>
                          <p-option value="biweek">2 Weeks</p-option>
                          <p-option value="month">1 Month</p-option>
                        </p-dropdown>
                      </div>

                      <div class="p-field" appearance="outline">
                        <label>Optimization Priority</label>
                        <p-dropdown formControlName="priority">
                          <p-option value="cost">Cost Optimization</p-option>
                          <p-option value="coverage">Maximum Coverage</p-option>
                          <p-option value="balance">Work-Life Balance</p-option>
                          <p-option value="skills">Skill Matching</p-option>
                        </p-dropdown>
                      </div>
                    </div>

                    <div class="ai-features">
                      <h4>AI Features Enabled:</h4>
                      <div class="feature-list">
                        <div class="feature-item">
                          <i class="pi pi-check-circle"></i>
                          <span>Staff availability analysis</span>
                        </div>
                        <div class="feature-item">
                          <i class="pi pi-check-circle"></i>
                          <span>Time off integration</span>
                        </div>
                        <div class="feature-item">
                          <i class="pi pi-check-circle"></i>
                          <span>Hours of operation compliance</span>
                        </div>
                        <div class="feature-item">
                          <i class="pi pi-check-circle"></i>
                          <span>Workload balancing</span>
                        </div>
                        <div class="feature-item">
                          <i class="pi pi-check-circle"></i>
                          <span>Skill-based assignments</span>
                        </div>
                      </div>
                    </div>
                  </form>
                </ng-template>

                <p-card-actions>
                  <button p-button color="accent" (click)="generateAISchedule()" [disabled]="isGenerating">
                    <mat-icon *ngIf="!isGenerating">auto_awesome</mat-icon>
                    <mat-spinner *ngIf="isGenerating" diameter="20"></mat-spinner>
                    {{ isGenerating ? 'Generating AI Schedule...' : 'Generate AI Schedule' }}
                  </button>
                  <button mat-button (click)="previewAISchedule()" [disabled]="isGenerating">
                    <mat-icon>preview</mat-icon>
                    Preview
                  </button>
                </ng-template>
              </p-card>

              <!-- AI Schedule Results -->
              <p-card class="ai-results" *ngIf="aiScheduleResults">
                <p-card-header>
                  <p-card-title>
                    <mat-icon>auto_awesome</mat-icon>
                    AI Generated Schedule
                  </ng-template>
                  <p-card-subtitle>Optimized schedule with {{ aiScheduleResults.confidence }}% confidence</ng-template>
                </ng-template>

                <p-card-content>
                  <div class="ai-insights">
                    <h4>AI Insights:</h4>
                    <div [innerHTML]="aiScheduleResults.insights"></div>
                  </div>

                  <div class="schedule-preview">
                    <p>Schedule preview will be displayed here</p>
                  </div>
                </ng-template>

                <p-card-actions>
                  <button p-button color="primary" (click)="applyAISchedule()">
                    <mat-icon>check</mat-icon>
                    Apply Schedule
                  </button>
                  <button mat-button (click)="modifyAISchedule()">
                    <i class="pi pi-pencil"></i>
                    Modify
                  </button>
                  <button mat-button (click)="regenerateAISchedule()">
                    <mat-icon>refresh</mat-icon>
                    Regenerate
                  </button>
                </ng-template>
              </p-card>
            </div>
          </div>
        </p-tabPanel>

        <!-- Schedule Templates Tab -->
        <p-tabPanel label="Templates">
          <div class="tab-content">
            <div class="templates-section">
              <p-card class="templates-card">
                <p-card-header>
                  <p-card-title>
                    <mat-icon>library_books</mat-icon>
                    Schedule Templates
                  </ng-template>
                  <p-card-subtitle>Save and reuse common scheduling patterns</ng-template>
                </ng-template>

                <p-card-content>
                  <div class="templates-placeholder">
                    <i class="pi pi-clock"></i>
                    <p>Schedule templates will be available here</p>
                    <button p-button color="primary">
                      <i class="pi pi-plus"></i>
                      Create Template
                    </button>
                  </div>
                </ng-template>
              </p-card>
            </div>
          </div>
        </p-tabPanel>
      </p-tabView>
    </div>
  `,
  styleUrls: ['./scheduling-interface.component.scss']
})
export class SchedulingInterfaceComponent implements OnInit {
  private aiService = inject(AIAssistantService);
  private authService = inject(AuthService);
  private fb = inject(FormBuilder);

  // Forms
  manualScheduleForm: FormGroup;
  aiScheduleForm: FormGroup;

  // State
  isGenerating = false;
  aiScheduleResults: any = null;

  constructor() {
    this.manualScheduleForm = this.fb.group({
      weekStart: [new Date()],
      department: ['all'],
      template: ['standard'],
      coverage: ['standard']
    });

    this.aiScheduleForm = this.fb.group({
      period: ['week'],
      priority: ['balance']
    });
  }

  ngOnInit(): void {
    // Initialize component
  }

  createManualSchedule(): void {
    console.log('Create manual schedule');
  }

  buildManualSchedule(): void {
    console.log('Build manual schedule with:', this.manualScheduleForm.value);
  }

  resetForm(): void {
    this.manualScheduleForm.reset();
  }

  async generateAISchedule(): Promise<void> {
    this.isGenerating = true;
    
    try {
      // TODO: Implement AI schedule generation using Gemini 2.5 Flash
      await new Promise(resolve => setTimeout(resolve, 3000)); // Simulate API call
      
      this.aiScheduleResults = {
        confidence: 92,
        insights: `
          <p><strong>Optimization Results:</strong></p>
          <ul>
            <li>Achieved 95% coverage with optimal staff distribution</li>
            <li>Balanced workload across all team members</li>
            <li>Respected all availability constraints and time off requests</li>
            <li>Minimized overtime costs while maintaining service levels</li>
          </ul>
        `
      };
    } catch (error) {
      console.error('AI schedule generation failed:', error);
    } finally {
      this.isGenerating = false;
    }
  }

  previewAISchedule(): void {
    console.log('Preview AI schedule');
  }

  applyAISchedule(): void {
    console.log('Apply AI schedule');
  }

  modifyAISchedule(): void {
    console.log('Modify AI schedule');
  }

  regenerateAISchedule(): void {
    this.aiScheduleResults = null;
    this.generateAISchedule();
  }
}

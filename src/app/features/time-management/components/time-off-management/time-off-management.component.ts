import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Observable } from 'rxjs';

// Angular Material
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatChipsModule } from '@angular/material/chips';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatBadgeModule } from '@angular/material/badge';

// Services
import { AuthService } from '../../../../core/auth/auth.service';

export interface TimeOffRequest {
  id: string;
  staffId: string;
  staffName: string;
  type: 'vacation' | 'sick' | 'personal' | 'other';
  startDate: Date;
  endDate: Date;
  totalDays: number;
  reason: string;
  status: 'pending' | 'approved' | 'denied';
  requestedAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
  notes?: string;
}

export interface TimeOffBalance {
  staffId: string;
  staffName: string;
  vacation: {
    allocated: number;
    used: number;
    pending: number;
    remaining: number;
  };
  sick: {
    allocated: number;
    used: number;
    pending: number;
    remaining: number;
  };
  personal: {
    allocated: number;
    used: number;
    pending: number;
    remaining: number;
  };
}

@Component({
  selector: 'app-time-off-management',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatChipsModule,
    MatTabsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatBadgeModule
  ],
  template: `
    <div class="time-off-container">
      <div class="time-off-header">
        <h2>
          <mat-icon>event_busy</mat-icon>
          Time Off Management
        </h2>
        <div class="header-actions">
          <button mat-raised-button color="primary" (click)="openRequestDialog()">
            <mat-icon>add</mat-icon>
            New Request
          </button>
          <button mat-raised-button color="accent" (click)="generateAIInsights()">
            <mat-icon>psychology</mat-icon>
            AI Insights
          </button>
        </div>
      </div>

      <mat-tab-group class="time-off-tabs">
        <!-- Pending Requests Tab -->
        <mat-tab>
          <ng-template mat-tab-label>
            <span>Pending Requests</span>
            <span *ngIf="pendingRequests.length > 0" class="pending-badge">{{ pendingRequests.length }}</span>
          </ng-template>
          <div class="tab-content">
            <div class="requests-grid">
              <mat-card *ngFor="let request of pendingRequests" class="request-card pending">
                <mat-card-header>
                  <div mat-card-avatar class="request-avatar">
                    <mat-icon>person</mat-icon>
                  </div>
                  <mat-card-title>{{ request.staffName }}</mat-card-title>
                  <mat-card-subtitle>
                    {{ request.type | titlecase }} • {{ request.totalDays }} day(s)
                  </mat-card-subtitle>
                </mat-card-header>

                <mat-card-content>
                  <div class="request-details">
                    <div class="date-range">
                      <mat-icon>date_range</mat-icon>
                      <span>{{ request.startDate | date:'MMM d, y' }} - {{ request.endDate | date:'MMM d, y' }}</span>
                    </div>
                    <div class="reason" *ngIf="request.reason">
                      <mat-icon>notes</mat-icon>
                      <span>{{ request.reason }}</span>
                    </div>
                    <div class="requested-date">
                      <mat-icon>schedule</mat-icon>
                      <span>Requested {{ request.requestedAt | date:'MMM d, y' }}</span>
                    </div>
                  </div>
                </mat-card-content>

                <mat-card-actions>
                  <button mat-raised-button color="primary" (click)="approveRequest(request.id)">
                    <mat-icon>check</mat-icon>
                    Approve
                  </button>
                  <button mat-raised-button color="warn" (click)="denyRequest(request.id)">
                    <mat-icon>close</mat-icon>
                    Deny
                  </button>
                  <button mat-button (click)="viewDetails(request.id)">
                    <mat-icon>visibility</mat-icon>
                    Details
                  </button>
                </mat-card-actions>
              </mat-card>
            </div>

            <div class="empty-state" *ngIf="pendingRequests.length === 0">
              <mat-icon>check_circle</mat-icon>
              <h3>No Pending Requests</h3>
              <p>All time off requests have been reviewed.</p>
            </div>
          </div>
        </mat-tab>

        <!-- All Requests Tab -->
        <mat-tab label="All Requests">
          <div class="tab-content">
            <div class="requests-table">
              <table mat-table [dataSource]="allRequests" class="requests-table">
                <ng-container matColumnDef="staff">
                  <th mat-header-cell *matHeaderCellDef>Staff</th>
                  <td mat-cell *matCellDef="let request">{{ request.staffName }}</td>
                </ng-container>

                <ng-container matColumnDef="type">
                  <th mat-header-cell *matHeaderCellDef>Type</th>
                  <td mat-cell *matCellDef="let request">
                    <mat-chip [class]="'type-' + request.type">{{ request.type | titlecase }}</mat-chip>
                  </td>
                </ng-container>

                <ng-container matColumnDef="dates">
                  <th mat-header-cell *matHeaderCellDef>Dates</th>
                  <td mat-cell *matCellDef="let request">
                    {{ request.startDate | date:'MMM d' }} - {{ request.endDate | date:'MMM d, y' }}
                  </td>
                </ng-container>

                <ng-container matColumnDef="days">
                  <th mat-header-cell *matHeaderCellDef>Days</th>
                  <td mat-cell *matCellDef="let request">{{ request.totalDays }}</td>
                </ng-container>

                <ng-container matColumnDef="status">
                  <th mat-header-cell *matHeaderCellDef>Status</th>
                  <td mat-cell *matCellDef="let request">
                    <mat-chip [class]="'status-' + request.status">{{ request.status | titlecase }}</mat-chip>
                  </td>
                </ng-container>

                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef>Actions</th>
                  <td mat-cell *matCellDef="let request">
                    <button mat-icon-button (click)="viewDetails(request.id)">
                      <mat-icon>visibility</mat-icon>
                    </button>
                    <button mat-icon-button *ngIf="request.status === 'pending'" (click)="editRequest(request.id)">
                      <mat-icon>edit</mat-icon>
                    </button>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
              </table>
            </div>
          </div>
        </mat-tab>

        <!-- Balances Tab -->
        <mat-tab label="Time Off Balances">
          <div class="tab-content">
            <div class="balances-grid">
              <mat-card *ngFor="let balance of timeOffBalances" class="balance-card">
                <mat-card-header>
                  <div mat-card-avatar class="balance-avatar">
                    <mat-icon>person</mat-icon>
                  </div>
                  <mat-card-title>{{ balance.staffName }}</mat-card-title>
                </mat-card-header>

                <mat-card-content>
                  <div class="balance-summary">
                    <div class="balance-item">
                      <h4>Vacation</h4>
                      <div class="balance-details">
                        <span class="remaining">{{ balance.vacation.remaining }} days left</span>
                        <div class="balance-breakdown">
                          Used: {{ balance.vacation.used }} | Pending: {{ balance.vacation.pending }}
                        </div>
                      </div>
                    </div>

                    <div class="balance-item">
                      <h4>Sick Time</h4>
                      <div class="balance-details">
                        <span class="remaining">{{ balance.sick.remaining }} days left</span>
                        <div class="balance-breakdown">
                          Used: {{ balance.sick.used }} | Pending: {{ balance.sick.pending }}
                        </div>
                      </div>
                    </div>

                    <div class="balance-item">
                      <h4>Personal</h4>
                      <div class="balance-details">
                        <span class="remaining">{{ balance.personal.remaining }} days left</span>
                        <div class="balance-breakdown">
                          Used: {{ balance.personal.used }} | Pending: {{ balance.personal.pending }}
                        </div>
                      </div>
                    </div>
                  </div>
                </mat-card-content>

                <mat-card-actions>
                  <button mat-button color="primary" (click)="adjustBalance(balance.staffId)">
                    <mat-icon>edit</mat-icon>
                    Adjust
                  </button>
                  <button mat-button (click)="viewHistory(balance.staffId)">
                    <mat-icon>history</mat-icon>
                    History
                  </button>
                </mat-card-actions>
              </mat-card>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  `,
  styleUrls: ['./time-off-management.component.scss']
})
export class TimeOffManagementComponent implements OnInit {
  private authService = inject(AuthService);
  private fb = inject(FormBuilder);

  // Data
  pendingRequests: TimeOffRequest[] = [];
  allRequests: TimeOffRequest[] = [];
  timeOffBalances: TimeOffBalance[] = [];
  displayedColumns = ['staff', 'type', 'dates', 'days', 'status', 'actions'];

  ngOnInit(): void {
    this.loadTimeOffData();
  }

  private loadTimeOffData(): void {
    // TODO: Load from Firestore
    this.loadMockData();
  }

  private loadMockData(): void {
    // Mock data for demonstration
    this.pendingRequests = [
      {
        id: '1',
        staffId: 'staff1',
        staffName: 'John Doe',
        type: 'vacation',
        startDate: new Date('2024-02-15'),
        endDate: new Date('2024-02-19'),
        totalDays: 5,
        reason: 'Family vacation',
        status: 'pending',
        requestedAt: new Date('2024-01-15')
      }
    ];

    this.allRequests = [...this.pendingRequests];

    this.timeOffBalances = [
      {
        staffId: 'staff1',
        staffName: 'John Doe',
        vacation: { allocated: 20, used: 5, pending: 5, remaining: 10 },
        sick: { allocated: 10, used: 2, pending: 0, remaining: 8 },
        personal: { allocated: 5, used: 1, pending: 0, remaining: 4 }
      }
    ];
  }

  openRequestDialog(): void {
    // TODO: Open time off request dialog
    console.log('Open request dialog');
  }

  approveRequest(requestId: string): void {
    // TODO: Approve request
    console.log('Approve request:', requestId);
  }

  denyRequest(requestId: string): void {
    // TODO: Deny request
    console.log('Deny request:', requestId);
  }

  viewDetails(requestId: string): void {
    // TODO: View request details
    console.log('View details:', requestId);
  }

  editRequest(requestId: string): void {
    // TODO: Edit request
    console.log('Edit request:', requestId);
  }

  adjustBalance(staffId: string): void {
    // TODO: Adjust time off balance
    console.log('Adjust balance for:', staffId);
  }

  viewHistory(staffId: string): void {
    // TODO: View time off history
    console.log('View history for:', staffId);
  }

  async generateAIInsights(): Promise<void> {
    // TODO: Generate AI insights for time off patterns
    console.log('Generate AI insights');
  }
}

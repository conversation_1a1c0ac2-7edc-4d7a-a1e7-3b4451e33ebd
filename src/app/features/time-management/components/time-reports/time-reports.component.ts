import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-time-reports',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule
  ],
  template: `
    <div class="time-reports-container">
      <div class="placeholder-content">
        <mat-card class="placeholder-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>assessment</mat-icon>
              Time Reports & Analytics
            </mat-card-title>
            <mat-card-subtitle>Comprehensive time and attendance reporting</mat-card-subtitle>
          </mat-card-header>
          
          <mat-card-content>
            <div class="placeholder-message">
              <mat-icon>bar_chart</mat-icon>
              <h3>Time Reports Coming Soon</h3>
              <p>This feature will include:</p>
              <ul>
                <li>Attendance reports and summaries</li>
                <li>Overtime and labor cost analysis</li>
                <li>Productivity metrics and trends</li>
                <li>Time off usage reports</li>
                <li>AI-powered insights and recommendations</li>
                <li>Exportable reports (PDF, Excel)</li>
              </ul>
            </div>
          </mat-card-content>
          
          <mat-card-actions>
            <button mat-raised-button color="primary" disabled>
              <mat-icon>file_download</mat-icon>
              Generate Report
            </button>
            <button mat-button disabled>
              <mat-icon>psychology</mat-icon>
              AI Insights
            </button>
          </mat-card-actions>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .time-reports-container {
      padding: 24px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .placeholder-card {
      max-width: 500px;
      width: 100%;
    }

    .placeholder-message {
      text-align: center;
      padding: 24px;

      mat-icon {
        font-size: 64px;
        width: 64px;
        height: 64px;
        color: #1976d2;
        margin-bottom: 16px;
      }

      h3 {
        margin: 0 0 16px 0;
        color: #333;
      }

      p {
        margin: 0 0 16px 0;
        color: #666;
      }

      ul {
        text-align: left;
        color: #555;
        
        li {
          margin-bottom: 8px;
        }
      }
    }

    mat-card-actions {
      display: flex;
      gap: 12px;
      justify-content: center;
    }
  `]
})
export class TimeReportsComponent {}

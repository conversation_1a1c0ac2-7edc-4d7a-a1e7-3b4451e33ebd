import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Observable, combineLatest } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';

// Angular Material
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatListModule } from '@angular/material/list';
import { MatTabsModule } from '@angular/material/tabs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBarModule } from '@angular/material/snack-bar';

// Services
import { AIAssistantService, AIRecommendation, SchedulingSuggestion } from '../../core/services/ai-assistant.service';
import { AuthService } from '../../core/auth/auth.service';
import { StaffFirestoreService } from '../staff/services/staff-firestore.service';

@Component({
  selector: 'app-ai-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressBarModule,
    MatListModule,
    MatTabsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSnackBarModule
  ],
  template: `
    <div class="ai-dashboard-container">
      <div class="dashboard-header">
        <h1>
          <mat-icon>psychology</mat-icon>
          AI Assistant Dashboard
        </h1>
        <p>Intelligent insights and recommendations for your team</p>
      </div>

      <mat-tab-group class="ai-tabs" animationDuration="300ms">
        <!-- Recommendations Tab -->
        <mat-tab label="Smart Recommendations">
          <div class="tab-content">
            <div class="recommendations-grid">
              <mat-card *ngFor="let recommendation of recommendations$ | async"
                        class="recommendation-card"
                        [class]="'priority-' + recommendation.priority">
                <mat-card-header>
                  <mat-card-title>
                    <mat-icon>{{ getRecommendationIcon(recommendation.type) }}</mat-icon>
                    {{ recommendation.title }}
                  </mat-card-title>
                  <mat-card-subtitle>
                    <mat-chip [class]="'confidence-' + getConfidenceLevel(recommendation.confidence)">
                      {{ (recommendation.confidence * 100) | number:'1.0-0' }}% confidence
                    </mat-chip>
                  </mat-card-subtitle>
                </mat-card-header>

                <mat-card-content>
                  <p>{{ recommendation.description }}</p>

                  <div class="action-items" *ngIf="recommendation.actionItems.length > 0">
                    <h4>Recommended Actions:</h4>
                    <ul>
                      <li *ngFor="let action of recommendation.actionItems">{{ action }}</li>
                    </ul>
                  </div>

                  <div class="affected-staff" *ngIf="recommendation.affectedStaff?.length">
                    <h4>Affected Staff:</h4>
                    <mat-chip-set>
                      <mat-chip *ngFor="let staffId of recommendation.affectedStaff">
                        {{ getStaffName(staffId) }}
                      </mat-chip>
                    </mat-chip-set>
                  </div>
                </mat-card-content>

                <mat-card-actions>
                  <button mat-button color="primary" (click)="implementRecommendation(recommendation)">
                    Implement
                  </button>
                  <button mat-button (click)="dismissRecommendation(recommendation)">
                    Dismiss
                  </button>
                </mat-card-actions>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Scheduling Assistant Tab -->
        <mat-tab label="Smart Scheduling">
          <div class="tab-content">
            <div class="scheduling-section">
              <mat-card class="scheduling-card">
                <mat-card-header>
                  <mat-card-title>
                    <mat-icon>schedule</mat-icon>
                    Scheduling Suggestions
                  </mat-card-title>
                  <mat-card-subtitle>AI-powered staff scheduling recommendations</mat-card-subtitle>
                </mat-card-header>

                <mat-card-content>
                  <div class="date-selector">
                    <mat-form-field appearance="outline">
                      <mat-label>Select Date</mat-label>
                      <input matInput type="date" [(ngModel)]="selectedDate" (change)="loadSchedulingSuggestions()">
                    </mat-form-field>
                    <button mat-raised-button color="primary" (click)="loadSchedulingSuggestions()">
                      Get Suggestions
                    </button>
                  </div>

                  <div class="suggestions-list" *ngIf="schedulingSuggestions$ | async as suggestions">
                    <mat-list>
                      <mat-list-item *ngFor="let suggestion of suggestions">
                        <mat-icon matListItemIcon>person</mat-icon>
                        <div matListItemTitle>{{ suggestion.staffName }}</div>
                        <div matListItemLine>
                          {{ suggestion.suggestedTime | date:'short' }} - {{ suggestion.reason }}
                        </div>
                        <mat-chip matListItemMeta [class]="'confidence-' + getConfidenceLevel(suggestion.confidence)">
                          {{ (suggestion.confidence * 100) | number:'1.0-0' }}%
                        </mat-chip>
                      </mat-list-item>
                    </mat-list>
                  </div>
                </mat-card-content>

                <mat-card-actions>
                  <button mat-raised-button color="primary" (click)="generateOptimalSchedule()">
                    Generate Full Schedule
                  </button>
                </mat-card-actions>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- AI Chat Assistant Tab -->
        <mat-tab label="AI Chat">
          <div class="tab-content">
            <mat-card class="chat-card">
              <mat-card-header>
                <mat-card-title>
                  <mat-icon>chat</mat-icon>
                  AI Staff Management Assistant
                </mat-card-title>
                <mat-card-subtitle>Ask questions about staff management, scheduling, and optimization</mat-card-subtitle>
              </mat-card-header>

              <mat-card-content>
                <div class="chat-messages" #chatMessages>
                  <div *ngFor="let message of chatHistory"
                       class="chat-message"
                       [class]="message.type">
                    <div class="message-content">
                      <strong>{{ message.type === 'user' ? 'You' : 'AI Assistant' }}:</strong>
                      <p>{{ message.content }}</p>
                    </div>
                    <div class="message-time">{{ message.timestamp | date:'short' }}</div>
                  </div>
                </div>

                <div class="chat-input">
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Ask the AI Assistant...</mat-label>
                    <textarea matInput
                              [(ngModel)]="currentQuestion"
                              (keydown.enter)="sendMessage()"
                              placeholder="e.g., How can I improve team productivity?"
                              rows="3"></textarea>
                  </mat-form-field>
                  <button mat-raised-button
                          color="primary"
                          (click)="sendMessage()"
                          [disabled]="!currentQuestion.trim() || isProcessing">
                    <mat-icon *ngIf="!isProcessing">send</mat-icon>
                    <mat-icon *ngIf="isProcessing" class="spinning">refresh</mat-icon>
                    {{ isProcessing ? 'Processing...' : 'Send' }}
                  </button>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>

        <!-- Advanced AI Analytics Tab -->
        <mat-tab label="Advanced Analytics">
          <div class="tab-content">
            <div class="insights-grid">
              <mat-card class="insight-card">
                <mat-card-header>
                  <mat-card-title>
                    <mat-icon>analytics</mat-icon>
                    Performance Analysis
                  </mat-card-title>
                  <mat-card-subtitle>Powered by Gemini 2.5 Flash</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <p>Deep AI analysis of staff performance patterns and trends</p>
                  <button mat-raised-button color="primary" (click)="runPerformanceAnalysis()">
                    <mat-icon>play_arrow</mat-icon>
                    Analyze Performance
                  </button>
                  <div *ngIf="performanceAnalysis" class="analysis-result">
                    <h4>Analysis Results:</h4>
                    <div [innerHTML]="formatAnalysisText(performanceAnalysis)"></div>
                  </div>
                </mat-card-content>
              </mat-card>

              <mat-card class="insight-card">
                <mat-card-header>
                  <mat-card-title>
                    <mat-icon>groups</mat-icon>
                    Team Dynamics
                  </mat-card-title>
                  <mat-card-subtitle>AI-powered team analysis</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <p>Analyze team collaboration and identify improvement opportunities</p>
                  <button mat-raised-button color="primary" (click)="analyzeTeamDynamics()">
                    <mat-icon>psychology</mat-icon>
                    Analyze Team
                  </button>
                  <div *ngIf="teamAnalysis" class="analysis-result">
                    <h4>Team Analysis:</h4>
                    <div [innerHTML]="formatAnalysisText(teamAnalysis)"></div>
                  </div>
                </mat-card-content>
              </mat-card>

              <mat-card class="insight-card">
                <mat-card-header>
                  <mat-card-title>
                    <mat-icon>trending_up</mat-icon>
                    Predictive Staffing
                  </mat-card-title>
                  <mat-card-subtitle>Future workforce planning</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <p>AI predictions for future staffing needs and requirements</p>
                  <button mat-raised-button color="primary" (click)="predictStaffingNeeds()">
                    <mat-icon>visibility</mat-icon>
                    Generate Predictions
                  </button>
                  <div *ngIf="staffingPredictions" class="analysis-result">
                    <h4>Staffing Predictions:</h4>
                    <div [innerHTML]="formatAnalysisText(staffingPredictions)"></div>
                  </div>
                </mat-card-content>
              </mat-card>

              <mat-card class="insight-card">
                <mat-card-header>
                  <mat-card-title>
                    <mat-icon>auto_awesome</mat-icon>
                    AI Model Info
                  </mat-card-title>
                  <mat-card-subtitle>Gemini 2.5 Flash Preview</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <div class="model-info">
                    <p><strong>Model:</strong> gemini-2.5-flash-preview-04-17</p>
                    <p><strong>Capabilities:</strong></p>
                    <ul>
                      <li>Advanced reasoning and analysis</li>
                      <li>Complex data interpretation</li>
                      <li>Predictive insights</li>
                      <li>Natural language understanding</li>
                      <li>Structured data generation</li>
                    </ul>
                    <p><strong>Optimized for:</strong> Fast, accurate staff management insights</p>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  `,
  styleUrls: ['./ai-dashboard.component.scss']
})
export class AIDashboardComponent implements OnInit {
  private aiService = inject(AIAssistantService);
  private authService = inject(AuthService);
  private staffService = inject(StaffFirestoreService);

  recommendations$!: Observable<AIRecommendation[]>;
  schedulingSuggestions$!: Observable<SchedulingSuggestion[]>;

  selectedDate = new Date().toISOString().split('T')[0];
  currentQuestion = '';
  isProcessing = false;

  // Advanced Analytics Properties
  performanceAnalysis: string | null = null;
  teamAnalysis: string | null = null;
  staffingPredictions: string | null = null;

  chatHistory: Array<{
    type: 'user' | 'assistant';
    content: string;
    timestamp: Date;
  }> = [];

  ngOnInit(): void {
    this.loadRecommendations();
    this.loadSchedulingSuggestions();
  }

  private loadRecommendations(): void {
    this.authService.userProfile$.subscribe(profile => {
      if (profile?.primaryBusinessId) {
        this.recommendations$ = this.aiService.analyzeWorkloadBalance(profile.primaryBusinessId);
      }
    });
  }

  loadSchedulingSuggestions(): void {
    this.authService.userProfile$.subscribe(profile => {
      if (profile?.primaryBusinessId) {
        const date = new Date(this.selectedDate);
        this.schedulingSuggestions$ = this.aiService.getSchedulingRecommendations(
          profile.primaryBusinessId,
          date
        );
      }
    });
  }

  async sendMessage(): Promise<void> {
    if (!this.currentQuestion.trim() || this.isProcessing) return;

    const question = this.currentQuestion.trim();
    this.currentQuestion = '';
    this.isProcessing = true;

    // Add user message to chat
    this.chatHistory.push({
      type: 'user',
      content: question,
      timestamp: new Date()
    });

    try {
      // Get context for AI
      const context = {
        userRole: 'manager', // Get from auth service
        currentDate: new Date(),
        businessId: 'current-business-id' // Get from auth service
      };

      const response = await this.aiService.askStaffManagementQuestion(question, context);

      // Add AI response to chat
      this.chatHistory.push({
        type: 'assistant',
        content: response,
        timestamp: new Date()
      });
    } catch (error) {
      this.chatHistory.push({
        type: 'assistant',
        content: 'I apologize, but I encountered an error processing your request. Please try again.',
        timestamp: new Date()
      });
    } finally {
      this.isProcessing = false;
    }
  }

  generateOptimalSchedule(): void {
    // Implementation for generating optimal schedule
    console.log('Generating optimal schedule...');
  }

  implementRecommendation(recommendation: AIRecommendation): void {
    // Implementation for applying recommendation
    console.log('Implementing recommendation:', recommendation);
  }

  dismissRecommendation(recommendation: AIRecommendation): void {
    // Implementation for dismissing recommendation
    console.log('Dismissing recommendation:', recommendation);
  }

  getRecommendationIcon(type: string): string {
    const icons: { [key: string]: string } = {
      'scheduling': 'schedule',
      'task-assignment': 'assignment',
      'goal-optimization': 'flag',
      'workload-balance': 'balance'
    };
    return icons[type] || 'lightbulb';
  }

  getConfidenceLevel(confidence: number): string {
    if (confidence >= 0.8) return 'high';
    if (confidence >= 0.6) return 'medium';
    return 'low';
  }

  getStaffName(staffId: string): string {
    // This would need to be implemented to get staff name from ID
    return `Staff ${staffId.substring(0, 8)}`;
  }

  // Advanced Analytics Methods
  async runPerformanceAnalysis(): Promise<void> {
    try {
      this.performanceAnalysis = 'Analyzing performance data...';

      // Get sample staff data for analysis
      const staffData = [
        { id: '1', name: 'John Doe', performance: 85, tasks: 12, goals: 3 },
        { id: '2', name: 'Jane Smith', performance: 92, tasks: 15, goals: 4 }
      ];

      const performanceMetrics = {
        averagePerformance: 88.5,
        taskCompletionRate: 0.87,
        goalAchievementRate: 0.75
      };

      const analysis = await this.aiService.analyzeStaffPerformance(staffData, performanceMetrics);
      this.performanceAnalysis = analysis;
    } catch (error) {
      this.performanceAnalysis = 'Error analyzing performance data. Please try again.';
    }
  }

  async analyzeTeamDynamics(): Promise<void> {
    try {
      this.teamAnalysis = 'Analyzing team dynamics...';

      const teamData = {
        teamSize: 8,
        collaborationScore: 7.5,
        communicationRating: 8.2,
        conflictLevel: 'low',
        leadershipStyle: 'collaborative'
      };

      const analysis = await this.aiService.analyzeTeamDynamics(teamData);
      this.teamAnalysis = analysis;
    } catch (error) {
      this.teamAnalysis = 'Error analyzing team dynamics. Please try again.';
    }
  }

  async predictStaffingNeeds(): Promise<void> {
    try {
      this.staffingPredictions = 'Generating staffing predictions...';

      const historicalData = {
        pastYearStaffing: [8, 9, 10, 12, 11, 9, 8, 10, 11, 12, 10, 9],
        seasonalTrends: 'higher_demand_q4',
        turnoverRate: 0.15
      };

      const businessProjections = {
        expectedGrowth: 0.20,
        newProjects: 3,
        marketExpansion: true
      };

      const predictions = await this.aiService.predictStaffingNeeds(historicalData, businessProjections);
      this.staffingPredictions = predictions;
    } catch (error) {
      this.staffingPredictions = 'Error generating predictions. Please try again.';
    }
  }

  formatAnalysisText(text: string): string {
    if (!text) return '';

    // Convert markdown-like formatting to HTML
    return text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      .replace(/^/, '<p>')
      .replace(/$/, '</p>')
      .replace(/- (.*?)(<br>|$)/g, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/g, '<ul>$1</ul>');
  }
}

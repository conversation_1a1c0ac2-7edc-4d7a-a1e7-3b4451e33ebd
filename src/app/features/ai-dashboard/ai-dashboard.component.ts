import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Observable, combineLatest } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';

// Angular Material
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

import { ChipModule } from 'primeng/chip';
import { ProgressBarModule } from 'primeng/progressbar';
import { ListboxModule } from 'primeng/listbox';
import { TabViewModule } from 'primeng/tabview';

import { InputTextModule } from 'primeng/inputtext';
import { ToastModule } from 'primeng/toast';

// Services
import { AIAssistantService, AIRecommendation, SchedulingSuggestion } from '../../core/services/ai-assistant.service';
import { AuthService } from '../../core/auth/auth.service';
import { StaffFirestoreService } from '../staff/services/staff-firestore.service';

@Component({
  selector: 'app-ai-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CardModule,
    ButtonModule
    ChipModule,
    ProgressBarModule,
    ListboxModule,
    TabViewModule
    InputTextModule,
    ToastModule
  ],
  template: `
    <div class="ai-dashboard-container">
      <div class="dashboard-header">
        <h1>
          <i class="pi pi-brain"></i>
          AI Assistant Dashboard
        </h1>
        <p>Intelligent insights and recommendations for your team</p>
      </div>

      <p-tabView class="ai-tabs" animationDuration="300ms">
        <!-- Recommendations Tab -->
        <p-tabPanel label="Smart Recommendations">
          <div class="tab-content">
            <div class="recommendations-grid">
              <p-card *ngFor="let recommendation of recommendations$ | async"
                        class="recommendation-card"
                        [class]="'priority-' + recommendation.priority">
                <ng-template pTemplate="header">
                  <h3>
                    <i class="pi pi-circle"></i>
                    {{ recommendation.title }}
                  </ng-template>
                  <p>
                    <p-chip [class]="'confidence-' + getConfidenceLevel(recommendation.confidence)">
                      {{ (recommendation.confidence * 100) | number:'1.0-0' }}% confidence
                    </p-chip</ng-template>
                </ng-template>

                <ng-template pTemplate="content">
                  <p>{{ recommendation.description }}</p>

                  <div class="action-items" *ngIf="recommendation.actionItems.length > 0">
                    <h4>Recommended Actions:</h4>
                    <ul>
                      <li *ngFor="let action of recommendation.actionItems">{{ action }}</li>
                    </ul>
                  </div>

                  <div class="affected-staff" *ngIf="recommendation.affectedStaff?.length">
                    <h4>Affected Staff:</h4>
                    <p-chip-set>
                      <p-chip *ngFor="let staffId of recommendation.affectedStaff">
                        {{ getStaffName(staffId) }}
                      </p-chip>
                    </div>
                  </div</ng-template>

                <ng-template pTemplate="footer">
                  <button p-button color="primary" (click)="implementRecommendation(recommendation)">
                    Implement
                  </p-button>
                  <button p-button (click)="dismissRecommendation(recommendation)">
                    Dismiss
                  </p-button</ng-template></p-card>
            </div>
          </div>
        </p-tabPanel>

        <!-- Scheduling Assistant Tab -->
        <p-tabPanel label="Smart Scheduling">
          <div class="tab-content">
            <div class="scheduling-section">
              <p-card class="scheduling-card">
                <ng-template pTemplate="header">
                  <h3>
                    <i class="pi pi-clock"></i>
                    Scheduling Suggestions
                  </ng-template>
                  <p</ng-template>

                <ng-template pTemplate="content">
                  <div class="date-selector">
                    <div class="p-field" appearance="outline">
                      <label>Select Date</label>
                      <input pInputText type="date" [(ngModel)]="selectedDate" (change)="loadSchedulingSuggestions()">
                    </div>
                    <p-button color="primary" (click)="loadSchedulingSuggestions()">
                      Get Suggestions
                    </p-button>
                  </div>

                  <div class="suggestions-list" *ngIf="schedulingSuggestions$ | async as suggestions">
                    <mat-list>
                      <mat-list-item *ngFor="let suggestion of suggestions">
                        <i class="pi pi-user"></i>
                        <div matListItemTitle>{{ suggestion.staffName }}</div>
                        <div matListItemLine>
                          {{ suggestion.suggestedTime | date:'short' }} - {{ suggestion.reason }}
                        </div>
                        <p-chip matListItemMeta [class]="'confidence-' + getConfidenceLevel(suggestion.confidence)">
                          {{ (suggestion.confidence * 100) | number:'1.0-0' }}%
                        </p-chip>
                      </mat-list-item>
                    </mat-list>
                  </div</ng-template>

                <ng-template pTemplate="footer">
                  <p-button color="primary" (click)="generateOptimalSchedule()">
                    Generate Full Schedule
                  </p-button</ng-template></p-card>
            </div>
          </div>
        </p-tabPanel>

        <!-- AI Chat Assistant Tab -->
        <p-tabPanel label="AI Chat">
          <div class="tab-content">
            <p-card class="chat-card">
              <ng-template pTemplate="header">
                <h3>
                  <i class="pi pi-comments"></i>
                  AI Staff Management Assistant
                </ng-template>
                <p</ng-template>

              <ng-template pTemplate="content">
                <div class="chat-messages" #chatMessages>
                  <div *ngFor="let message of chatHistory"
                       class="chat-message"
                       [class]="message.type">
                    <div class="message-content">
                      <strong>{{ message.type === 'user' ? 'You' : 'AI Assistant' }}:</strong>
                      <p>{{ message.content }}</p>
                    </div>
                    <div class="message-time">{{ message.timestamp | date:'short' }}</div>
                  </div>
                </div>

                <div class="chat-input">
                  <div class="p-field" appearance="outline" class="full-width">
                    <label>Ask the AI Assistant...</label>
                    <textarea pInputText
                              [(ngModel)]="currentQuestion"
                              (keydown.enter)="sendMessage()"
                              placeholder="e.g., How can I improve team productivity?"
                              rows="3"></textarea>
                  </div>
                  <p-button
                          color="primary"
                          (click)="sendMessage()"
                          [disabled]="!currentQuestion.trim() || isProcessing">
                    <i class="pi pi-circle"></i>
                    <i class="pi pi-circle"></i>
                    {{ isProcessing ? 'Processing...' : 'Send' }}
                  </p-button>
                </div</ng-template></p-card>
          </div>
        </p-tabPanel>

        <!-- Advanced AI Analytics Tab -->
        <p-tabPanel label="Advanced Analytics">
          <div class="tab-content">
            <div class="insights-grid">
              <p-card class="insight-card">
                <ng-template pTemplate="header">
                  <h3>
                    <i class="pi pi-circle"></i>
                    Performance Analysis
                  </ng-template>
                  <p</ng-template>
                <ng-template pTemplate="content">
                  <p>Deep AI analysis of staff performance patterns and trends</p>
                  <p-button color="primary" (click)="runPerformanceAnalysis()">
                    <i class="pi pi-circle"></i>
                    Analyze Performance
                  </p-button>
                  <div *ngIf="performanceAnalysis" class="analysis-result">
                    <h4>Analysis Results:</h4>
                    <div [innerHTML]="formatAnalysisText(performanceAnalysis)"></div>
                  </div</ng-template></p-card>

              <p-card class="insight-card">
                <ng-template pTemplate="header">
                  <h3>
                    <i class="pi pi-circle"></i>
                    Team Dynamics
                  </ng-template>
                  <p</ng-template>
                <ng-template pTemplate="content">
                  <p>Analyze team collaboration and identify improvement opportunities</p>
                  <p-button color="primary" (click)="analyzeTeamDynamics()">
                    <i class="pi pi-brain"></i>
                    Analyze Team
                  </p-button>
                  <div *ngIf="teamAnalysis" class="analysis-result">
                    <h4>Team Analysis:</h4>
                    <div [innerHTML]="formatAnalysisText(teamAnalysis)"></div>
                  </div</ng-template></p-card>

              <p-card class="insight-card">
                <ng-template pTemplate="header">
                  <h3>
                    <i class="pi pi-circle"></i>
                    Predictive Staffing
                  </ng-template>
                  <p</ng-template>
                <ng-template pTemplate="content">
                  <p>AI predictions for future staffing needs and requirements</p>
                  <p-button color="primary" (click)="predictStaffingNeeds()">
                    <i class="pi pi-eye"></i>
                    Generate Predictions
                  </p-button>
                  <div *ngIf="staffingPredictions" class="analysis-result">
                    <h4>Staffing Predictions:</h4>
                    <div [innerHTML]="formatAnalysisText(staffingPredictions)"></div>
                  </div</ng-template></p-card>

              <p-card class="insight-card">
                <ng-template pTemplate="header">
                  <h3>
                    <i class="pi pi-sparkles"></i>
                    AI Model Info
                  </ng-template>
                  <p</ng-template>
                <ng-template pTemplate="content">
                  <div class="model-info">
                    <p><strong>Model:</strong> gemini-2.5-flash-preview-04-17</p>
                    <p><strong>Capabilities:</strong></p>
                    <ul>
                      <li>Advanced reasoning and analysis</li>
                      <li>Complex data interpretation</li>
                      <li>Predictive insights</li>
                      <li>Natural language understanding</li>
                      <li>Structured data generation</li>
                    </ul>
                    <p><strong>Optimized for:</strong> Fast, accurate staff management insights</p>
                  </div</ng-template></p-card>
            </div>
          </div>
        </p-tabPanel>
      </p-tabView>
    </div>
  `,
  styleUrls: ['./ai-dashboard.component.scss']
})
export class AIDashboardComponent implements OnInit {
  private aiService = inject(AIAssistantService);
  private authService = inject(AuthService);
  private staffService = inject(StaffFirestoreService);

  recommendations$!: Observable<AIRecommendation[]>;
  schedulingSuggestions$!: Observable<SchedulingSuggestion[]>;

  selectedDate = new Date().toISOString().split('T')[0];
  currentQuestion = '';
  isProcessing = false;

  // Advanced Analytics Properties
  performanceAnalysis: string | null = null;
  teamAnalysis: string | null = null;
  staffingPredictions: string | null = null;

  chatHistory: Array<{
    type: 'user' | 'assistant';
    content: string;
    timestamp: Date;
  }> = [];

  ngOnInit(): void {
    this.loadRecommendations();
    this.loadSchedulingSuggestions();
  }

  private loadRecommendations(): void {
    this.authService.userProfile$.subscribe(profile => {
      if (profile?.primaryBusinessId) {
        this.recommendations$ = this.aiService.analyzeWorkloadBalance(profile.primaryBusinessId);
      }
    });
  }

  loadSchedulingSuggestions(): void {
    this.authService.userProfile$.subscribe(profile => {
      if (profile?.primaryBusinessId) {
        const date = new Date(this.selectedDate);
        this.schedulingSuggestions$ = this.aiService.getSchedulingRecommendations(
          profile.primaryBusinessId,
          date
        );
      }
    });
  }

  async sendMessage(): Promise<void> {
    if (!this.currentQuestion.trim() || this.isProcessing) return;

    const question = this.currentQuestion.trim();
    this.currentQuestion = '';
    this.isProcessing = true;

    // Add user message to chat
    this.chatHistory.push({
      type: 'user',
      content: question,
      timestamp: new Date()
    });

    try {
      // Get context for AI
      const context = {
        userRole: 'manager', // Get from auth service
        currentDate: new Date(),
        businessId: 'current-business-id' // Get from auth service
      };

      const response = await this.aiService.askStaffManagementQuestion(question, context);

      // Add AI response to chat
      this.chatHistory.push({
        type: 'assistant',
        content: response,
        timestamp: new Date()
      });
    } catch (error) {
      this.chatHistory.push({
        type: 'assistant',
        content: 'I apologize, but I encountered an error processing your request. Please try again.',
        timestamp: new Date()
      });
    } finally {
      this.isProcessing = false;
    }
  }

  generateOptimalSchedule(): void {
    // Implementation for generating optimal schedule
    console.log('Generating optimal schedule...');
  }

  implementRecommendation(recommendation: AIRecommendation): void {
    // Implementation for applying recommendation
    console.log('Implementing recommendation:', recommendation);
  }

  dismissRecommendation(recommendation: AIRecommendation): void {
    // Implementation for dismissing recommendation
    console.log('Dismissing recommendation:', recommendation);
  }

  getRecommendationIcon(type: string): string {
    const icons: { [key: string]: string } = {
      'scheduling': 'schedule',
      'task-assignment': 'assignment',
      'goal-optimization': 'flag',
      'workload-balance': 'balance'
    };
    return icons[type] || 'lightbulb';
  }

  getConfidenceLevel(confidence: number): string {
    if (confidence >= 0.8) return 'high';
    if (confidence >= 0.6) return 'medium';
    return 'low';
  }

  getStaffName(staffId: string): string {
    // This would need to be implemented to get staff name from ID
    return `Staff ${staffId.substring(0, 8)}`;
  }

  // Advanced Analytics Methods
  async runPerformanceAnalysis(): Promise<void> {
    try {
      this.performanceAnalysis = 'Analyzing performance data...';

      // Get sample staff data for analysis
      const staffData = [
        { id: '1', name: 'John Doe', performance: 85, tasks: 12, goals: 3 },
        { id: '2', name: 'Jane Smith', performance: 92, tasks: 15, goals: 4 }
      ];

      const performanceMetrics = {
        averagePerformance: 88.5,
        taskCompletionRate: 0.87,
        goalAchievementRate: 0.75
      };

      const analysis = await this.aiService.analyzeStaffPerformance(staffData, performanceMetrics);
      this.performanceAnalysis = analysis;
    } catch (error) {
      this.performanceAnalysis = 'Error analyzing performance data. Please try again.';
    }
  }

  async analyzeTeamDynamics(): Promise<void> {
    try {
      this.teamAnalysis = 'Analyzing team dynamics...';

      const teamData = {
        teamSize: 8,
        collaborationScore: 7.5,
        communicationRating: 8.2,
        conflictLevel: 'low',
        leadershipStyle: 'collaborative'
      };

      const analysis = await this.aiService.analyzeTeamDynamics(teamData);
      this.teamAnalysis = analysis;
    } catch (error) {
      this.teamAnalysis = 'Error analyzing team dynamics. Please try again.';
    }
  }

  async predictStaffingNeeds(): Promise<void> {
    try {
      this.staffingPredictions = 'Generating staffing predictions...';

      const historicalData = {
        pastYearStaffing: [8, 9, 10, 12, 11, 9, 8, 10, 11, 12, 10, 9],
        seasonalTrends: 'higher_demand_q4',
        turnoverRate: 0.15
      };

      const businessProjections = {
        expectedGrowth: 0.20,
        newProjects: 3,
        marketExpansion: true
      };

      const predictions = await this.aiService.predictStaffingNeeds(historicalData, businessProjections);
      this.staffingPredictions = predictions;
    } catch (error) {
      this.staffingPredictions = 'Error generating predictions. Please try again.';
    }
  }

  formatAnalysisText(text: string): string {
    if (!text) return '';

    // Convert markdown-like formatting to HTML
    return text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      .replace(/^/, '<p>')
      .replace(/$/, '</p>')
      .replace(/- (.*?)(<br>|$)/g, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/g, '<ul>$1</ul>');
  }
}

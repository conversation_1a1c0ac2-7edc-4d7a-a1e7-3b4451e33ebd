import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { CardModule } from 'primeng/card';

import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';

import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ToastModule } from 'primeng/toast';
import { DropdownModule } from 'primeng/dropdown';
import { AuthService } from '../../../core/auth/auth.service';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    CardModule
    InputTextModule,
    ButtonModule
    ProgressSpinnerModule,
    ToastModule,
    DropdownModule
  ],
  template: `
    <div class="register-container">
      <p-card class="register-card">
        <p-card-header>
          <p-card-title>
            <i class="pi pi-user-plus"></i>
            Create Account
          </ng-template>
          <p-card-subtitle>Join <PERSON>anager</ng-template>
        </ng-template>

        <p-card-content>
          <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
            <div class="p-field" appearance="outline" class="full-width">
              <label>Full Name</label>
              <input matInput formControlName="displayName" required>
              <i class="pi pi-user"></i>
              <mat-error *ngIf="registerForm.get('displayName')?.hasError('required')">
                Full name is required
              </mat-error>
            </div>

            <div class="p-field" appearance="outline" class="full-width">
              <label>Email</label>
              <input matInput type="email" formControlName="email" required>
              <mat-icon matSuffix>email</mat-icon>
              <mat-error *ngIf="registerForm.get('email')?.hasError('required')">
                Email is required
              </mat-error>
              <mat-error *ngIf="registerForm.get('email')?.hasError('email')">
                Please enter a valid email
              </mat-error>
            </div>

            <div class="p-field" appearance="outline" class="full-width">
              <label>Password</label>
              <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password" required>
              <button p-button [text]="true" matSuffix (click)="hidePassword = !hidePassword" type="button">
                <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
              </button>
              <mat-error *ngIf="registerForm.get('password')?.hasError('required')">
                Password is required
              </mat-error>
              <mat-error *ngIf="registerForm.get('password')?.hasError('minlength')">
                Password must be at least 6 characters
              </mat-error>
            </div>

            <div class="p-field" appearance="outline" class="full-width">
              <label>Confirm Password</label>
              <input matInput [type]="hideConfirmPassword ? 'password' : 'text'" formControlName="confirmPassword" required>
              <button p-button [text]="true" matSuffix (click)="hideConfirmPassword = !hideConfirmPassword" type="button">
                <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
              </button>
              <mat-error *ngIf="registerForm.get('confirmPassword')?.hasError('required')">
                Please confirm your password
              </mat-error>
              <mat-error *ngIf="registerForm.hasError('passwordMismatch')">
                Passwords do not match
              </mat-error>
            </div>

            <div class="p-field" appearance="outline" class="full-width">
              <label>Role</label>
              <p-dropdown formControlName="role" required>
                <p-option value="staff">Staff</p-option>
                <p-option value="manager">Manager</p-option>
                <p-option value="admin">Admin</p-option>
              </p-dropdown>
              <mat-error *ngIf="registerForm.get('role')?.hasError('required')">
                Please select a role
              </mat-error>
            </div>

            <button p-button color="primary" type="submit"
                    class="full-width register-button"
                    [disabled]="registerForm.invalid || loading">
              <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
              <span *ngIf="!loading">Create Account</span>
            </button>
          </form>
        </ng-template>

        <p-card-actions>
          <p>Already have an account?
            <a mat-button color="primary" routerLink="/auth/login">Sign in</a>
          </p>
        </ng-template>
      </p-card>
    </div>
  `,
  styles: [`
    .register-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .register-card {
      width: 100%;
      max-width: 400px;
      padding: 20px;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .register-button {
      height: 48px;
      margin: 20px 0;
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    mat-card-actions p {
      margin: 0;
      text-align: center;
    }
  `]
})
export class RegisterComponent {
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private router = inject(Router);
  private snackBar = inject(MatSnackBar);

  registerForm: FormGroup;
  loading = false;
  hidePassword = true;
  hideConfirmPassword = true;

  constructor() {
    this.registerForm = this.fb.group({
      displayName: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]],
      role: ['staff', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }
    return null;
  }

  onSubmit(): void {
    if (this.registerForm.valid && !this.loading) {
      this.loading = true;
      const { email, password, displayName, role } = this.registerForm.value;

      this.authService.signUpWithEmail(email, password, displayName, role).subscribe({
        next: (user) => {
          this.loading = false;
          if (user) {
            this.snackBar.open('Account created successfully!', 'Close', { duration: 3000 });
            this.router.navigate(['/dashboard']);
          }
        },
        error: (error) => {
          this.loading = false;
          this.snackBar.open(this.getErrorMessage(error), 'Close', { duration: 5000 });
        }
      });
    }
  }

  private getErrorMessage(error: any): string {
    switch (error.code) {
      case 'auth/email-already-in-use':
        return 'An account with this email already exists.';
      case 'auth/invalid-email':
        return 'Invalid email address.';
      case 'auth/weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'auth/operation-not-allowed':
        return 'Email/password accounts are not enabled.';
      default:
        return 'An error occurred during registration. Please try again.';
    }
  }
}

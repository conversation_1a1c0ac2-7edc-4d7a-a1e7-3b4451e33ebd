import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

// Angular Material
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

import { ChipModule } from 'primeng/chip';
import { ProgressBarModule } from 'primeng/progressbar';
import { MenuModule } from 'primeng/menu';
import { DividerModule } from 'primeng/divider';
import { TooltipModule } from 'primeng/tooltip';

// Models
import { StaffGoalExtended } from '../../staff/models/staff.model';

@Component({
  selector: 'app-goal-card',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    ButtonModule
    ChipModule,
    ProgressBarModule,
    MenuModule,
    DividerModule,
    TooltipModule
  ],
  template: `
    <p-card class="goal-card" [class]="'goal-' + goal.status">
      <ng-template pTemplate="header">
        <div mat-card-avatar class="goal-avatar" [class]="'avatar-' + goal.type">
          <i class="pi pi-circle"></i>
        </div>
        <h3>
        <p>
        <div class="card-actions" *ngIf="canEdit">
          <p-button [text]="true" [matMenuTriggerFor]="goalMenu">
            <i class="pi pi-ellipsis-v"></i>
          </p-button>
          <p-menu #goalMenu="matMenu">
            <button mat-menu-item (click)="onEdit()">
              <i class="pi pi-pencil"></i>
              <span>Edit Goal</span>
            </p-button>
            <button mat-menu-item (click)="onViewDetails()">
              <i class="pi pi-eye"></i>
              <span>View Details</span>
            </p-button>
            <p-divider></p-divider>
            <button mat-menu-item (click)="onDelete()" class="delete-action">
              <i class="pi pi-trash"></i>
              <span>Delete Goal</span>
            </p-button>
          </p-menu>
        </div</ng-template>

      <ng-template pTemplate="content">
        <p class="goal-description">{{ goal.description }}</p>

        <!-- Progress Section -->
        <div class="goal-progress">
          <div class="progress-header">
            <span class="progress-label">Progress</span>
            <span class="progress-value">{{ goal.progress }}%</span>
          </div>
          <p-progressBar 
            mode="determinate" 
            [value]="goal.progress"
            [class]="getProgressBarClass()">
          </p-progressBar>
        </div>

        <!-- Target Value (if applicable) -->
        <div class="goal-target" *ngIf="goal.targetValue && goal.unit">
          <div class="target-info">
            <span class="target-label">Target:</span>
            <span class="target-value">
              {{ goal.currentValue || 0 }} / {{ goal.targetValue }} {{ goal.unit }}
            </span>
          </div>
          <div class="target-progress">
            <p-progressBar 
              mode="determinate" 
              [value]="getTargetProgress()"
              color="accent">
            </p-progressBar>
          </div>
        </div>

        <!-- Due Date -->
        <div class="goal-due-date">
          <i class="pi pi-circle"></i>
          <span class="due-date-text" [class]="getDueDateClass()">
            Due: {{ goal.targetDate | date:'mediumDate' }}
          </span>
          <span class="days-remaining" *ngIf="getDaysRemaining() !== null">
            ({{ getDaysRemaining() }} days {{ getDaysRemaining()! > 0 ? 'remaining' : 'overdue' }})
          </span>
        </div>

        <!-- Status and Priority Chips -->
        <div class="goal-chips">
          <p-chip [class]="'status-chip status-' + goal.status">
            <i class="pi pi-circle"></i>
            {{ goal.status | titlecase }}
          </p-chip>
          <p-chip [class]="'priority-chip priority-' + goal.priority">
            {{ goal.priority | titlecase }}
          </p-chip>
        </div>

        <!-- Milestones (if any) -->
        <div class="goal-milestones" *ngIf="goal.milestones && goal.milestones.length > 0">
          <div class="milestones-header">
            <i class="pi pi-circle"></i>
            <span>Milestones ({{ getCompletedMilestones() }}/{{ goal.milestones.length }})</span>
          </div>
          <div class="milestones-list">
            <div 
              *ngFor="let milestone of goal.milestones.slice(0, 3)" 
              class="milestone-item"
              [class.completed]="milestone.completed">
              <i class="pi pi-circle"></i>
              <span class="milestone-title">{{ milestone.title }}</span>
            </div>
            <div *ngIf="goal.milestones.length > 3" class="more-milestones">
              +{{ goal.milestones.length - 3 }} more milestones
            </div>
          </div>
        </div>

        <!-- Tags (if any) -->
        <div class="goal-tags" *ngIf="goal.tags && goal.tags.length > 0">
          <p-chip *ngFor="let tag of goal.tags.slice(0, 3)" class="tag-chip">
            {{ tag }}
          </p-chip>
          <span *ngIf="goal.tags.length > 3" class="more-tags">
            +{{ goal.tags.length - 3 }} more
          </span>
        </div</ng-template>

      <ng-template pTemplate="footer">
        <button p-button (click)="onViewDetails()">
          <i class="pi pi-eye"></i>
          View Details
        </p-button>
        <button p-button (click)="onEdit()" *ngIf="canEdit">
          <i class="pi pi-pencil"></i>
          Edit
        </p-button>
        <div class="spacer"></div>
        <span class="last-updated" pTooltip="Last updated">
          {{ goal.updatedAt | date:'short' }}
        </span</ng-template></p-card>
  `,
  styles: [`
    .goal-card {
      height: 100%;
      display: flex;
      flex-direction: column;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      position: relative;
    }

    .goal-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    }

    .goal-card.goal-completed {
      border-left: 4px solid #4caf50;
    }

    .goal-card.goal-in-progress {
      border-left: 4px solid #2196f3;
    }

    .goal-card.goal-overdue {
      border-left: 4px solid #f44336;
    }

    .goal-card.goal-not-started {
      border-left: 4px solid #ff9800;
    }

    .goal-avatar {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      color: white;
    }

    .avatar-individual {
      background-color: #2196f3;
    }

    .avatar-team {
      background-color: #4caf50;
    }

    .avatar-company {
      background-color: #ff9800;
    }

    .card-actions {
      margin-left: auto;
    }

    .goal-description {
      color: var(--mdc-theme-text-secondary-on-background);
      margin-bottom: 16px;
      line-height: 1.5;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .goal-progress {
      margin-bottom: 16px;
    }

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .progress-label {
      font-weight: 500;
      color: var(--mdc-theme-text-primary-on-background);
    }

    .progress-value {
      font-weight: 600;
      color: var(--mdc-theme-primary);
    }

    .goal-target {
      margin-bottom: 16px;
      padding: 12px;
      background-color: rgba(0,0,0,0.02);
      border-radius: 4px;
    }

    .target-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 0.875rem;
    }

    .target-label {
      color: var(--mdc-theme-text-secondary-on-background);
    }

    .target-value {
      font-weight: 500;
    }

    .goal-due-date {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      font-size: 0.875rem;
    }

    .date-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .due-date-text.overdue {
      color: #f44336;
      font-weight: 500;
    }

    .due-date-text.due-soon {
      color: #ff9800;
      font-weight: 500;
    }

    .days-remaining {
      color: var(--mdc-theme-text-secondary-on-background);
      font-size: 0.75rem;
    }

    .goal-chips {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }

    .status-chip {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .status-chip mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .status-completed {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .status-in-progress {
      background-color: #e3f2fd;
      color: #1565c0;
    }

    .status-overdue {
      background-color: #ffebee;
      color: #c62828;
    }

    .status-not-started {
      background-color: #fff3e0;
      color: #ef6c00;
    }

    .priority-high {
      background-color: #ffebee;
      color: #c62828;
    }

    .priority-medium {
      background-color: #fff3e0;
      color: #ef6c00;
    }

    .priority-low {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .priority-critical {
      background-color: #fce4ec;
      color: #ad1457;
    }

    .goal-milestones {
      margin-bottom: 16px;
    }

    .milestones-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      font-weight: 500;
      font-size: 0.875rem;
    }

    .milestones-header mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .milestone-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px 0;
      font-size: 0.875rem;
    }

    .milestone-item.completed {
      color: var(--mdc-theme-text-secondary-on-background);
      text-decoration: line-through;
    }

    .milestone-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .milestone-item.completed .milestone-icon {
      color: #4caf50;
    }

    .more-milestones {
      font-size: 0.75rem;
      color: var(--mdc-theme-text-secondary-on-background);
      margin-top: 4px;
    }

    .goal-tags {
      display: flex;
      gap: 4px;
      flex-wrap: wrap;
      margin-bottom: 16px;
    }

    .tag-chip {
      font-size: 0.75rem;
      height: 24px;
      background-color: rgba(0,0,0,0.05);
    }

    .more-tags {
      font-size: 0.75rem;
      color: var(--mdc-theme-text-secondary-on-background);
      align-self: center;
    }

    mat-card-actions {
      margin-top: auto;
      display: flex;
      align-items: center;
    }

    .spacer {
      flex: 1;
    }

    .last-updated {
      font-size: 0.75rem;
      color: var(--mdc-theme-text-secondary-on-background);
    }

    .delete-action {
      color: #f44336;
    }

    .delete-action mat-icon {
      color: #f44336;
    }
  `]
})
export class GoalCardComponent {
  @Input() goal!: StaffGoalExtended;
  @Input() canEdit = false;

  @Output() edit = new EventEmitter<StaffGoalExtended>();
  @Output() delete = new EventEmitter<StaffGoalExtended>();
  @Output() viewDetails = new EventEmitter<StaffGoalExtended>();

  getGoalIcon(): string {
    switch (this.goal.type) {
      case 'individual': return 'person';
      case 'team': return 'group';
      case 'company': return 'business';
      default: return 'flag';
    }
  }

  getStatusIcon(): string {
    switch (this.goal.status) {
      case 'completed': return 'check_circle';
      case 'in-progress': return 'play_circle';
      case 'overdue': return 'error';
      case 'not-started': return 'radio_button_unchecked';
      default: return 'help';
    }
  }

  getProgressBarClass(): string {
    if (this.goal.progress >= 100) return 'progress-complete';
    if (this.goal.progress >= 75) return 'progress-high';
    if (this.goal.progress >= 50) return 'progress-medium';
    if (this.goal.progress >= 25) return 'progress-low';
    return 'progress-minimal';
  }

  getTargetProgress(): number {
    if (!this.goal.targetValue || !this.goal.currentValue) return 0;
    return Math.min((this.goal.currentValue / this.goal.targetValue) * 100, 100);
  }

  getDaysRemaining(): number | null {
    if (!this.goal.targetDate) return null;
    const today = new Date();
    const targetDate = new Date(this.goal.targetDate);
    const diffTime = targetDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  getDueDateClass(): string {
    const daysRemaining = this.getDaysRemaining();
    if (daysRemaining === null) return '';
    if (daysRemaining < 0) return 'overdue';
    if (daysRemaining <= 7) return 'due-soon';
    return '';
  }

  getCompletedMilestones(): number {
    if (!this.goal.milestones) return 0;
    return this.goal.milestones.filter(m => m.completed).length;
  }

  onEdit(): void {
    this.edit.emit(this.goal);
  }

  onDelete(): void {
    this.delete.emit(this.goal);
  }

  onViewDetails(): void {
    this.viewDetails.emit(this.goal);
  }
}

import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Observable, map, switchMap } from 'rxjs';

// Angular Material
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatMenuModule } from '@angular/material/menu';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSnackBar } from '@angular/material/snack-bar';

// Services
import { StaffFirestoreService } from '../../staff/services/staff-firestore.service';
import { AuthService } from '../../../core/auth/auth.service';

// Models
import { StaffGoalExtended, StaffMember, GoalComment, GoalMilestone } from '../../staff/models/staff.model';

@Component({
  selector: 'app-goal-details',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressBarModule,
    MatTabsModule,
    MatDividerModule,
    MatListModule,
    MatFormFieldModule,
    MatInputModule,
    MatToolbarModule,
    MatMenuModule,
    MatExpansionModule,
    MatCheckboxModule
  ],
  template: `
    <div class="goal-details-container" *ngIf="goal$ | async as goal">
      <mat-toolbar color="primary">
        <button mat-icon-button (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <span>Goal Details</span>
        <span class="spacer"></span>
        <button mat-icon-button [matMenuTriggerFor]="goalMenu">
          <mat-icon>more_vert</mat-icon>
        </button>
        <mat-menu #goalMenu="matMenu">
          <button mat-menu-item (click)="editGoal(goal)">
            <mat-icon>edit</mat-icon>
            <span>Edit Goal</span>
          </button>
          <button mat-menu-item (click)="duplicateGoal(goal)">
            <mat-icon>content_copy</mat-icon>
            <span>Duplicate Goal</span>
          </button>
          <mat-divider></mat-divider>
          <button mat-menu-item (click)="deleteGoal(goal)" class="delete-action">
            <mat-icon>delete</mat-icon>
            <span>Delete Goal</span>
          </button>
        </mat-menu>
      </mat-toolbar>

      <div class="goal-details-content">
        <!-- Goal Header -->
        <mat-card class="goal-header-card">
          <mat-card-header>
            <div mat-card-avatar class="goal-avatar" [class]="'avatar-' + goal.type">
              <mat-icon>{{ getGoalIcon(goal.type) }}</mat-icon>
            </div>
            <mat-card-title>{{ goal.title }}</mat-card-title>
            <mat-card-subtitle>
              {{ goal.category | titlecase }} • {{ goal.type | titlecase }}
            </mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <p class="goal-description">{{ goal.description }}</p>

            <!-- Status and Priority -->
            <div class="goal-chips">
              <mat-chip [class]="'status-chip status-' + goal.status">
                <mat-icon>{{ getStatusIcon(goal.status) }}</mat-icon>
                {{ goal.status | titlecase }}
              </mat-chip>
              <mat-chip [class]="'priority-chip priority-' + goal.priority">
                {{ goal.priority | titlecase }}
              </mat-chip>
            </div>

            <!-- Progress Overview -->
            <div class="progress-section">
              <div class="progress-header">
                <h3>Progress Overview</h3>
                <span class="progress-value">{{ goal.progress }}%</span>
              </div>
              <mat-progress-bar mode="determinate" [value]="goal.progress" class="main-progress"></mat-progress-bar>

              <!-- Target Values -->
              <div class="target-section" *ngIf="goal.targetValue && goal.unit">
                <div class="target-info">
                  <span>Target: {{ goal.targetValue }} {{ goal.unit }}</span>
                  <span>Current: {{ goal.currentValue || 0 }} {{ goal.unit }}</span>
                  <span>Remaining: {{ goal.targetValue - (goal.currentValue || 0) }} {{ goal.unit }}</span>
                </div>
                <mat-progress-bar mode="determinate" [value]="getTargetProgress(goal)" color="accent"></mat-progress-bar>
              </div>
            </div>

            <!-- Timeline -->
            <div class="timeline-section">
              <div class="timeline-item">
                <mat-icon>schedule</mat-icon>
                <span>Due Date: {{ goal.targetDate | date:'fullDate' }}</span>
                <span class="days-remaining" [class]="getDueDateClass(goal)">
                  ({{ getDaysRemaining(goal) }} days {{ getDaysRemaining(goal) > 0 ? 'remaining' : 'overdue' }})
                </span>
              </div>
              <div class="timeline-item">
                <mat-icon>event</mat-icon>
                <span>Created: {{ goal.createdAt | date:'mediumDate' }}</span>
              </div>
              <div class="timeline-item">
                <mat-icon>update</mat-icon>
                <span>Last Updated: {{ goal.updatedAt | date:'medium' }}</span>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Tabs for detailed information -->
        <mat-tab-group class="goal-tabs">
          <!-- Milestones Tab -->
          <mat-tab>
            <ng-template mat-tab-label>
              Milestones
              <span class="tab-badge" *ngIf="goal.milestones?.length">
                ({{ getCompletedMilestones(goal) }}/{{ goal.milestones?.length }})
              </span>
            </ng-template>
            <div class="tab-content">
              <div class="milestones-section">
                <div class="section-header">
                  <h3>Goal Milestones</h3>
                  <button mat-raised-button color="primary" (click)="addMilestone()">
                    <mat-icon>add</mat-icon>
                    Add Milestone
                  </button>
                </div>

                <div class="milestones-list" *ngIf="goal.milestones && goal.milestones.length > 0">
                  <mat-expansion-panel *ngFor="let milestone of goal.milestones; let i = index"
                                       class="milestone-panel"
                                       [class.completed]="milestone.completed">
                    <mat-expansion-panel-header>
                      <mat-panel-title>
                        <mat-checkbox [checked]="milestone.completed"
                                      (change)="toggleMilestone(goal, i, $event.checked)"
                                      (click)="$event.stopPropagation()">
                        </mat-checkbox>
                        <span [class.completed-text]="milestone.completed">{{ milestone.title }}</span>
                      </mat-panel-title>
                      <mat-panel-description>
                        Due: {{ milestone.targetDate | date:'mediumDate' }}
                      </mat-panel-description>
                    </mat-expansion-panel-header>

                    <div class="milestone-details">
                      <p>Target Date: {{ milestone.targetDate | date:'fullDate' }}</p>
                      <div class="milestone-actions">
                        <button mat-button (click)="editMilestone(goal, i)">
                          <mat-icon>edit</mat-icon>
                          Edit
                        </button>
                        <button mat-button color="warn" (click)="deleteMilestone(goal, i)">
                          <mat-icon>delete</mat-icon>
                          Delete
                        </button>
                      </div>
                    </div>
                  </mat-expansion-panel>
                </div>

                <div class="empty-state" *ngIf="!goal.milestones || goal.milestones.length === 0">
                  <mat-icon>flag</mat-icon>
                  <h4>No milestones yet</h4>
                  <p>Break down this goal into smaller, manageable milestones.</p>
                  <button mat-raised-button color="primary" (click)="addMilestone()">
                    <mat-icon>add</mat-icon>
                    Add First Milestone
                  </button>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Comments Tab -->
          <mat-tab>
            <ng-template mat-tab-label>
              Comments
              <span class="tab-badge" *ngIf="goal.comments?.length">
                ({{ goal.comments?.length }})
              </span>
            </ng-template>
            <div class="tab-content">
              <div class="comments-section">
                <!-- Add Comment Form -->
                <mat-card class="add-comment-card">
                  <mat-card-content>
                    <form [formGroup]="commentForm" (ngSubmit)="addComment(goal)">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Add a comment</mat-label>
                        <textarea matInput formControlName="content" rows="3"
                                 placeholder="Share updates, ask questions, or provide feedback..."></textarea>
                      </mat-form-field>
                      <div class="comment-actions">
                        <button mat-raised-button color="primary" type="submit"
                                [disabled]="commentForm.invalid">
                          <mat-icon>send</mat-icon>
                          Post Comment
                        </button>
                      </div>
                    </form>
                  </mat-card-content>
                </mat-card>

                <!-- Comments List -->
                <div class="comments-list" *ngIf="goal.comments && goal.comments.length > 0">
                  <mat-card *ngFor="let comment of goal.comments" class="comment-card">
                    <mat-card-header>
                      <mat-card-title>{{ comment.userName }}</mat-card-title>
                      <mat-card-subtitle>{{ comment.createdAt | date:'medium' }}</mat-card-subtitle>
                    </mat-card-header>
                    <mat-card-content>
                      <p>{{ comment.content }}</p>
                    </mat-card-content>
                  </mat-card>
                </div>

                <div class="empty-state" *ngIf="!goal.comments || goal.comments.length === 0">
                  <mat-icon>comment</mat-icon>
                  <h4>No comments yet</h4>
                  <p>Start the conversation by adding the first comment.</p>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Assigned Staff Tab -->
          <mat-tab>
            <ng-template mat-tab-label>
              Assigned Staff
              <span class="tab-badge" *ngIf="goal.assignedTo?.length">
                ({{ goal.assignedTo.length }})
              </span>
            </ng-template>
            <div class="tab-content">
              <div class="assigned-staff-section">
                <div class="section-header">
                  <h3>Assigned Team Members</h3>
                  <button mat-raised-button color="primary" (click)="manageAssignments(goal)">
                    <mat-icon>person_add</mat-icon>
                    Manage Assignments
                  </button>
                </div>

                <div class="staff-list" *ngIf="assignedStaff$ | async as staff">
                  <mat-card *ngFor="let member of staff" class="staff-card">
                    <mat-card-header>
                      <mat-card-title>{{ member.firstName }} {{ member.lastName }}</mat-card-title>
                      <mat-card-subtitle>{{ member.position }}</mat-card-subtitle>
                    </mat-card-header>
                    <mat-card-content>
                      <p>{{ member.department }}</p>
                    </mat-card-content>
                    <mat-card-actions>
                      <button mat-button (click)="viewStaffProfile(member.id)">
                        <mat-icon>person</mat-icon>
                        View Profile
                      </button>
                    </mat-card-actions>
                  </mat-card>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Tags & Metadata Tab -->
          <mat-tab label="Details">
            <div class="tab-content">
              <div class="metadata-section">
                <h3>Goal Metadata</h3>

                <div class="metadata-grid">
                  <div class="metadata-item">
                    <label>Goal ID:</label>
                    <span>{{ goal.id }}</span>
                  </div>
                  <div class="metadata-item">
                    <label>Created By:</label>
                    <span>{{ goal.assignedBy }}</span>
                  </div>
                  <div class="metadata-item">
                    <label>Recurring:</label>
                    <span>{{ goal.isRecurring ? 'Yes (' + (goal.recurringPattern | titlecase) + ')' : 'No' }}</span>
                  </div>
                </div>

                <div class="tags-section" *ngIf="goal.tags && goal.tags.length > 0">
                  <h4>Tags</h4>
                  <div class="tags-list">
                    <mat-chip *ngFor="let tag of goal.tags" class="tag-chip">
                      {{ tag }}
                    </mat-chip>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </div>
    </div>

    <div class="loading-state" *ngIf="!(goal$ | async)">
      <mat-icon>hourglass_empty</mat-icon>
      <p>Loading goal details...</p>
    </div>
  `,
  styles: [`
    .goal-details-container {
      min-height: 100vh;
      background-color: #f5f5f5;
    }

    .spacer {
      flex: 1;
    }

    .goal-details-content {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .goal-header-card {
      margin-bottom: 24px;
    }

    .goal-avatar {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      border-radius: 50%;
      color: white;
    }

    .avatar-individual { background-color: #2196f3; }
    .avatar-team { background-color: #4caf50; }
    .avatar-company { background-color: #ff9800; }

    .goal-description {
      font-size: 1.1rem;
      line-height: 1.6;
      margin-bottom: 16px;
      color: var(--mdc-theme-text-secondary-on-background);
    }

    .goal-chips {
      display: flex;
      gap: 8px;
      margin-bottom: 24px;
      flex-wrap: wrap;
    }

    .status-chip, .priority-chip {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .status-chip mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .progress-section {
      margin-bottom: 24px;
    }

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .progress-header h3 {
      margin: 0;
      font-size: 1.2rem;
    }

    .progress-value {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--mdc-theme-primary);
    }

    .main-progress {
      height: 12px;
      border-radius: 6px;
      margin-bottom: 16px;
    }

    .target-section {
      padding: 16px;
      background-color: rgba(0,0,0,0.02);
      border-radius: 8px;
    }

    .target-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 0.875rem;
    }

    .timeline-section {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .timeline-item {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 0.875rem;
    }

    .timeline-item mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
      color: var(--mdc-theme-text-secondary-on-background);
    }

    .days-remaining.overdue {
      color: #f44336;
      font-weight: 500;
    }

    .days-remaining.due-soon {
      color: #ff9800;
      font-weight: 500;
    }

    .goal-tabs {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .tab-content {
      padding: 24px;
    }

    .tab-badge {
      margin-left: 8px;
      font-size: 0.75rem;
      opacity: 0.7;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .section-header h3 {
      margin: 0;
    }

    .milestone-panel {
      margin-bottom: 8px;
    }

    .milestone-panel.completed {
      opacity: 0.7;
    }

    .completed-text {
      text-decoration: line-through;
      color: var(--mdc-theme-text-secondary-on-background);
    }

    .milestone-details {
      padding: 16px 0;
    }

    .milestone-actions {
      display: flex;
      gap: 8px;
      margin-top: 16px;
    }

    .add-comment-card {
      margin-bottom: 24px;
    }

    .full-width {
      width: 100%;
    }

    .comment-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
    }

    .comment-card {
      margin-bottom: 16px;
    }

    .staff-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 16px;
    }

    .metadata-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      margin-bottom: 24px;
    }

    .metadata-item {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .metadata-item label {
      font-weight: 500;
      color: var(--mdc-theme-text-secondary-on-background);
    }

    .tags-list {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .tag-chip {
      background-color: rgba(0,0,0,0.05);
    }

    .empty-state {
      text-align: center;
      padding: 48px 24px;
      color: var(--mdc-theme-text-secondary-on-background);
    }

    .empty-state mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 50vh;
      color: var(--mdc-theme-text-secondary-on-background);
    }

    .loading-state mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
      animation: spin 2s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .delete-action {
      color: #f44336;
    }

    .delete-action mat-icon {
      color: #f44336;
    }

    @media (max-width: 768px) {
      .goal-details-content {
        padding: 16px;
      }

      .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
      }

      .target-info {
        flex-direction: column;
        gap: 4px;
      }

      .staff-list {
        grid-template-columns: 1fr;
      }

      .metadata-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class GoalDetailsComponent implements OnInit {
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private staffService = inject(StaffFirestoreService);
  private authService = inject(AuthService);
  private snackBar = inject(MatSnackBar);
  private fb = inject(FormBuilder);

  goal$!: Observable<StaffGoalExtended | null>;
  assignedStaff$!: Observable<StaffMember[]>;
  commentForm!: FormGroup;

  ngOnInit(): void {
    this.initializeCommentForm();
    this.loadGoalDetails();
  }

  private initializeCommentForm(): void {
    this.commentForm = this.fb.group({
      content: ['', [Validators.required, Validators.minLength(1)]]
    });
  }

  private loadGoalDetails(): void {
    this.goal$ = this.route.paramMap.pipe(
      map(params => params.get('id')),
      switchMap(id => {
        if (!id) {
          this.router.navigate(['/goals']);
          return [];
        }
        // Use the goals service to get a single goal
        return this.staffService.getGoalById(id);
      })
    );

    this.assignedStaff$ = this.goal$.pipe(
      switchMap(goal => {
        if (!goal?.assignedTo?.length) return [];
        // Get staff members assigned to this goal
        return this.staffService.subscribeToStaff().pipe(
          map(staff => staff.filter(s => goal.assignedTo.includes(s.id)))
        );
      })
    );
  }

  getGoalIcon(type: string): string {
    switch (type) {
      case 'individual': return 'person';
      case 'team': return 'group';
      case 'company': return 'business';
      default: return 'flag';
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'completed': return 'check_circle';
      case 'in-progress': return 'play_circle';
      case 'overdue': return 'error';
      case 'not-started': return 'radio_button_unchecked';
      default: return 'help';
    }
  }

  getTargetProgress(goal: StaffGoalExtended): number {
    if (!goal.targetValue || !goal.currentValue) return 0;
    return Math.min((goal.currentValue / goal.targetValue) * 100, 100);
  }

  getDaysRemaining(goal: StaffGoalExtended): number {
    if (!goal.targetDate) return 0;
    const today = new Date();
    const targetDate = new Date(goal.targetDate);
    const diffTime = targetDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  getDueDateClass(goal: StaffGoalExtended): string {
    const daysRemaining = this.getDaysRemaining(goal);
    if (daysRemaining < 0) return 'overdue';
    if (daysRemaining <= 7) return 'due-soon';
    return '';
  }

  getCompletedMilestones(goal: StaffGoalExtended): number {
    if (!goal.milestones) return 0;
    return goal.milestones.filter(m => m.completed).length;
  }

  editGoal(goal: StaffGoalExtended): void {
    this.router.navigate(['/goals/edit', goal.id]);
  }

  duplicateGoal(goal: StaffGoalExtended): void {
    // Implement goal duplication
    this.snackBar.open('Goal duplication coming soon!', 'Close', { duration: 3000 });
  }

  deleteGoal(goal: StaffGoalExtended): void {
    if (confirm(`Are you sure you want to delete the goal "${goal.title}"?`)) {
      this.staffService.deleteGoal(goal.id).subscribe({
        next: () => {
          this.snackBar.open('Goal deleted successfully!', 'Close', { duration: 3000 });
          this.router.navigate(['/goals']);
        },
        error: (error) => {
          console.error('Error deleting goal:', error);
          this.snackBar.open('Error deleting goal. Please try again.', 'Close', { duration: 5000 });
        }
      });
    }
  }

  addComment(goal: StaffGoalExtended): void {
    if (this.commentForm.valid) {
      // Get current user from observable
      this.authService.user$.subscribe(currentUser => {
        if (!currentUser) return;

        const newComment: GoalComment = {
          id: Date.now().toString(), // Temporary ID
          userId: currentUser.uid,
          userName: currentUser.displayName || currentUser.email || 'Unknown User',
          content: this.commentForm.value.content,
          createdAt: new Date()
        };

        const updatedComments = [...(goal.comments || []), newComment];

        this.staffService.updateGoal(goal.id, { comments: updatedComments }).subscribe({
          next: () => {
            this.commentForm.reset();
            this.snackBar.open('Comment added successfully!', 'Close', { duration: 3000 });
          },
          error: (error) => {
            console.error('Error adding comment:', error);
            this.snackBar.open('Error adding comment. Please try again.', 'Close', { duration: 5000 });
          }
        });
      });
    }
  }

  toggleMilestone(goal: StaffGoalExtended, index: number, completed: boolean): void {
    if (!goal.milestones) return;

    const updatedMilestones = [...goal.milestones];
    updatedMilestones[index] = { ...updatedMilestones[index], completed };

    this.staffService.updateGoal(goal.id, { milestones: updatedMilestones }).subscribe({
      next: () => {
        this.snackBar.open('Milestone updated!', 'Close', { duration: 2000 });
      },
      error: (error) => {
        console.error('Error updating milestone:', error);
        this.snackBar.open('Error updating milestone.', 'Close', { duration: 3000 });
      }
    });
  }

  addMilestone(): void {
    this.snackBar.open('Add milestone functionality coming soon!', 'Close', { duration: 3000 });
  }

  editMilestone(goal: StaffGoalExtended, index: number): void {
    this.snackBar.open('Edit milestone functionality coming soon!', 'Close', { duration: 3000 });
  }

  deleteMilestone(goal: StaffGoalExtended, index: number): void {
    if (!goal.milestones) return;

    if (confirm('Are you sure you want to delete this milestone?')) {
      const updatedMilestones = goal.milestones.filter((_, i) => i !== index);

      this.staffService.updateGoal(goal.id, { milestones: updatedMilestones }).subscribe({
        next: () => {
          this.snackBar.open('Milestone deleted!', 'Close', { duration: 3000 });
        },
        error: (error) => {
          console.error('Error deleting milestone:', error);
          this.snackBar.open('Error deleting milestone.', 'Close', { duration: 3000 });
        }
      });
    }
  }

  manageAssignments(goal: StaffGoalExtended): void {
    this.snackBar.open('Manage assignments functionality coming soon!', 'Close', { duration: 3000 });
  }

  viewStaffProfile(staffId: string): void {
    this.router.navigate(['/staff/profile', staffId]);
  }

  goBack(): void {
    this.router.navigate(['/goals']);
  }
}

import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormArray } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Observable, map } from 'rxjs';

// Angular Material
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSliderModule } from '@angular/material/slider';
import { MatChipsModule } from '@angular/material/chips';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';

// Services
import { StaffFirestoreService } from '../../staff/services/staff-firestore.service';
import { AuthService } from '../../../core/auth/auth.service';

// Models
import { StaffGoalExtended, StaffMember, GoalMilestone } from '../../staff/models/staff.model';

@Component({
  selector: 'app-goal-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    MatCheckboxModule,
    MatSliderModule,
    MatChipsModule,
    MatAutocompleteModule,
    MatExpansionModule,
    MatDividerModule,
    MatToolbarModule
  ],
  template: `
    <div class="goal-dialog-container">
      <mat-toolbar color="primary">
        <button mat-icon-button (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <span>{{ isEditMode ? 'Edit Goal' : 'Create New Goal' }}</span>
        <span class="spacer"></span>
        <button mat-icon-button (click)="getAISuggestions()" matTooltip="Get AI Suggestions">
          <mat-icon>psychology</mat-icon>
        </button>
      </mat-toolbar>

      <div class="goal-form-content">
        <form [formGroup]="goalForm" (ngSubmit)="onSubmit()" class="goal-form">
          <!-- Basic Information -->
          <mat-card class="form-section">
            <mat-card-header>
              <mat-card-title>Basic Information</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Goal Title</mat-label>
                  <input matInput formControlName="title" placeholder="Enter goal title">
                  <mat-error *ngIf="goalForm.get('title')?.hasError('required')">
                    Title is required
                  </mat-error>
                  <mat-error *ngIf="goalForm.get('title')?.hasError('minlength')">
                    Title must be at least 3 characters
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Description</mat-label>
                  <textarea matInput formControlName="description" rows="3"
                           placeholder="Describe the goal and what success looks like"></textarea>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Goal Type</mat-label>
                  <mat-select formControlName="type">
                    <mat-option value="individual">Individual</mat-option>
                    <mat-option value="team">Team</mat-option>
                    <mat-option value="company">Company</mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Category</mat-label>
                  <mat-select formControlName="category">
                    <mat-option value="sales">Sales</mat-option>
                    <mat-option value="performance">Performance</mat-option>
                    <mat-option value="training">Training</mat-option>
                    <mat-option value="attendance">Attendance</mat-option>
                    <mat-option value="custom">Custom</mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Priority</mat-label>
                  <mat-select formControlName="priority">
                    <mat-option value="low">Low</mat-option>
                    <mat-option value="medium">Medium</mat-option>
                    <mat-option value="high">High</mat-option>
                    <mat-option value="critical">Critical</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Assignment and Timeline -->
          <mat-card class="form-section">
            <mat-card-header>
              <mat-card-title>Assignment & Timeline</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Assigned To</mat-label>
                  <mat-select formControlName="assignedTo" multiple>
                    <mat-option *ngFor="let staff of staffMembers$ | async" [value]="staff.id">
                      {{ staff.firstName }} {{ staff.lastName }} ({{ staff.position }})
                    </mat-option>
                  </mat-select>
                  <mat-hint>Select one or more staff members</mat-hint>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Target Date</mat-label>
                  <input matInput [matDatepicker]="targetDatePicker" formControlName="targetDate">
                  <mat-datepicker-toggle matIconSuffix [for]="targetDatePicker"></mat-datepicker-toggle>
                  <mat-datepicker #targetDatePicker></mat-datepicker>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Current Progress (%)</mat-label>
                  <input matInput type="number" formControlName="progress" min="0" max="100">
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Status</mat-label>
                  <mat-select formControlName="status">
                    <mat-option value="not-started">Not Started</mat-option>
                    <mat-option value="in-progress">In Progress</mat-option>
                    <mat-option value="completed">Completed</mat-option>
                    <mat-option value="overdue">Overdue</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Target Values (Optional) -->
          <mat-card class="form-section">
            <mat-card-header>
              <mat-card-title>Target Values (Optional)</mat-card-title>
              <mat-card-subtitle>Set measurable targets for this goal</mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Target Value</mat-label>
                  <input matInput type="number" formControlName="targetValue"
                         placeholder="e.g., 100">
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Current Value</mat-label>
                  <input matInput type="number" formControlName="currentValue"
                         placeholder="e.g., 25">
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Unit</mat-label>
                  <mat-select formControlName="unit">
                    <mat-option value="dollars">Dollars ($)</mat-option>
                    <mat-option value="units">Units</mat-option>
                    <mat-option value="hours">Hours</mat-option>
                    <mat-option value="percentage">Percentage (%)</mat-option>
                    <mat-option value="points">Points</mat-option>
                    <mat-option value="custom">Custom</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Milestones -->
          <mat-card class="form-section">
            <mat-card-header>
              <mat-card-title>Milestones</mat-card-title>
              <mat-card-subtitle>Break down the goal into smaller milestones</mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <div formArrayName="milestones">
                <div *ngFor="let milestone of milestones.controls; let i = index"
                     [formGroupName]="i" class="milestone-item">
                  <mat-form-field appearance="outline" class="milestone-title">
                    <mat-label>Milestone {{ i + 1 }}</mat-label>
                    <input matInput formControlName="title" placeholder="Milestone title">
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="milestone-date">
                    <mat-label>Target Date</mat-label>
                    <input matInput [matDatepicker]="milestonePicker" formControlName="targetDate">
                    <mat-datepicker-toggle matIconSuffix [for]="milestonePicker"></mat-datepicker-toggle>
                    <mat-datepicker #milestonePicker></mat-datepicker>
                  </mat-form-field>

                  <mat-checkbox formControlName="completed" class="milestone-completed">
                    Completed
                  </mat-checkbox>

                  <button mat-icon-button type="button" (click)="removeMilestone(i)"
                          color="warn" class="remove-milestone">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>

              <button mat-stroked-button type="button" (click)="addMilestone()" class="add-milestone">
                <mat-icon>add</mat-icon>
                Add Milestone
              </button>
            </mat-card-content>
          </mat-card>

          <!-- Advanced Options -->
          <mat-expansion-panel class="advanced-options">
            <mat-expansion-panel-header>
              <mat-panel-title>Advanced Options</mat-panel-title>
            </mat-expansion-panel-header>

            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Tags</mat-label>
                <input matInput formControlName="tagsInput"
                       placeholder="Enter tags separated by commas">
                <mat-hint>e.g., sales, q1, priority</mat-hint>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-checkbox formControlName="isRecurring">
                Recurring Goal
              </mat-checkbox>
            </div>

            <div class="form-row" *ngIf="goalForm.get('isRecurring')?.value">
              <mat-form-field appearance="outline">
                <mat-label>Recurrence Pattern</mat-label>
                <mat-select formControlName="recurringPattern">
                  <mat-option value="daily">Daily</mat-option>
                  <mat-option value="weekly">Weekly</mat-option>
                  <mat-option value="monthly">Monthly</mat-option>
                  <mat-option value="quarterly">Quarterly</mat-option>
                  <mat-option value="yearly">Yearly</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </mat-expansion-panel>

          <!-- Form Actions -->
          <div class="form-actions">
            <button mat-button type="button" (click)="goBack()">
              Cancel
            </button>
            <button mat-raised-button color="primary" type="submit"
                    [disabled]="goalForm.invalid || isSubmitting">
              <mat-icon *ngIf="isSubmitting">hourglass_empty</mat-icon>
              <mat-icon *ngIf="!isSubmitting">{{ isEditMode ? 'update' : 'save' }}</mat-icon>
              {{ isSubmitting ? 'Saving...' : (isEditMode ? 'Update Goal' : 'Create Goal') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  `,
  styles: [`
    .goal-dialog-container {
      min-height: 100vh;
      background-color: #f5f5f5;
    }

    .spacer {
      flex: 1;
    }

    .goal-form-content {
      padding: 24px;
      max-width: 1000px;
      margin: 0 auto;
    }

    .goal-form {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .form-section {
      margin-bottom: 24px;
    }

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }

    .form-row > * {
      flex: 1;
      min-width: 200px;
    }

    .full-width {
      width: 100%;
    }

    .milestone-item {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 16px;
      padding: 16px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background-color: #fafafa;
    }

    .milestone-title {
      flex: 2;
    }

    .milestone-date {
      flex: 1;
    }

    .milestone-completed {
      flex: 0 0 auto;
    }

    .remove-milestone {
      flex: 0 0 auto;
    }

    .add-milestone {
      margin-top: 16px;
    }

    .advanced-options {
      margin-bottom: 24px;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 16px;
      padding: 24px;
      background-color: white;
      border-top: 1px solid #e0e0e0;
      position: sticky;
      bottom: 0;
      z-index: 10;
    }

    @media (max-width: 768px) {
      .goal-form-content {
        padding: 16px;
      }

      .form-row {
        flex-direction: column;
      }

      .form-row > * {
        min-width: unset;
      }

      .milestone-item {
        flex-direction: column;
        align-items: stretch;
      }

      .milestone-completed {
        align-self: flex-start;
      }
    }
  `]
})
export class GoalDialogComponent implements OnInit {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private staffService = inject(StaffFirestoreService);
  private authService = inject(AuthService);
  private snackBar = inject(MatSnackBar);

  goalForm!: FormGroup;
  isEditMode = false;
  isSubmitting = false;
  goalId?: string;

  staffMembers$!: Observable<StaffMember[]>;

  ngOnInit(): void {
    this.initializeForm();
    this.loadStaffMembers();
    this.checkEditMode();
  }

  private initializeForm(): void {
    this.goalForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      type: ['individual', Validators.required],
      category: ['performance', Validators.required],
      priority: ['medium', Validators.required],
      assignedTo: [[], Validators.required],
      targetDate: [null, Validators.required],
      progress: [0, [Validators.min(0), Validators.max(100)]],
      status: ['not-started', Validators.required],
      targetValue: [null],
      currentValue: [null],
      unit: [''],
      milestones: this.fb.array([]),
      tagsInput: [''],
      isRecurring: [false],
      recurringPattern: ['monthly']
    });
  }

  private loadStaffMembers(): void {
    this.staffMembers$ = this.staffService.subscribeToStaff();
  }

  private checkEditMode(): void {
    this.goalId = this.route.snapshot.paramMap.get('id') || undefined;
    this.isEditMode = !!this.goalId;

    if (this.isEditMode && this.goalId) {
      this.loadGoalForEdit(this.goalId);
    }
  }

  private loadGoalForEdit(goalId: string): void {
    // Load goal data and populate form
    // This would need to be implemented with the actual service method
    console.log('Loading goal for edit:', goalId);
  }

  get milestones(): FormArray {
    return this.goalForm.get('milestones') as FormArray;
  }

  addMilestone(): void {
    const milestoneGroup = this.fb.group({
      title: ['', Validators.required],
      targetDate: [null, Validators.required],
      completed: [false]
    });
    this.milestones.push(milestoneGroup);
  }

  removeMilestone(index: number): void {
    this.milestones.removeAt(index);
  }

  onSubmit(): void {
    if (this.goalForm.valid) {
      this.isSubmitting = true;
      const formValue = this.goalForm.value;

      // Process tags
      const tags = formValue.tagsInput ?
        formValue.tagsInput.split(',').map((tag: string) => tag.trim()).filter((tag: string) => tag) :
        [];

      const goalData: Omit<StaffGoalExtended, 'id' | 'createdAt' | 'updatedAt'> = {
        title: formValue.title,
        description: formValue.description,
        type: formValue.type,
        category: formValue.category,
        priority: formValue.priority,
        assignedTo: formValue.assignedTo,
        assignedBy: '', // Will be set from current user
        createdBy: '', // Will be set from current user
        targetDate: formValue.targetDate,
        progress: formValue.progress,
        status: formValue.status,
        targetValue: formValue.targetValue,
        currentValue: formValue.currentValue,
        unit: formValue.unit,
        tags,
        milestones: formValue.milestones,
        isRecurring: formValue.isRecurring,
        recurringPattern: formValue.recurringPattern,
        comments: []
      };

      // Set current user info
      this.authService.user$.subscribe(user => {
        if (user) {
          goalData.assignedBy = user.uid;
          goalData.createdBy = user.uid;

          if (this.isEditMode) {
            this.staffService.updateGoal(this.goalId!, goalData).subscribe({
              next: () => {
                this.snackBar.open('Goal updated successfully!', 'Close', { duration: 3000 });
                this.router.navigate(['/goals']);
              },
              error: (error: any) => {
                console.error('Error updating goal:', error);
                this.snackBar.open('Error updating goal. Please try again.', 'Close', { duration: 5000 });
                this.isSubmitting = false;
              }
            });
          } else {
            this.staffService.createGoal(goalData).subscribe({
              next: () => {
                this.snackBar.open('Goal created successfully!', 'Close', { duration: 3000 });
                this.router.navigate(['/goals']);
              },
              error: (error: any) => {
                console.error('Error creating goal:', error);
                this.snackBar.open('Error creating goal. Please try again.', 'Close', { duration: 5000 });
                this.isSubmitting = false;
              }
            });
          }
        }
      });
    }
  }

  getAISuggestions(): void {
    // Implement AI suggestions for goal creation
    this.snackBar.open('AI suggestions coming soon!', 'Close', { duration: 3000 });
  }

  goBack(): void {
    this.router.navigate(['/goals']);
  }
}

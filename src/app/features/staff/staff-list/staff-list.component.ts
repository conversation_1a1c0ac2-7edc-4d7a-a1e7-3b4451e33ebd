import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { InputTextModule } from 'primeng/inputtext';
import { ChipModule } from 'primeng/chip';
import { BadgeModule } from 'primeng/badge';
import { RouterModule } from '@angular/router';
import { Observable } from 'rxjs';

import { StaffService } from '../services/staff.service';
import { StaffMember, StaffListResponse } from '../models/staff.model';

@Component({
  selector: 'app-staff-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    CardModule,
    ButtonModule,
    TableModule,
    InputTextModule,
    ChipModule,
    BadgeModule
  ],
  template: `
    <div class="staff-list-container">
      <!-- Header -->
      <div class="staff-header">
        <div class="header-left">
          <h1>
            <i class="pi pi-users"></i>
            Staff Directory
          </h1>
          <p>Manage your team members and their information</p>
        </div>
        <div class="header-actions">
          <button p-button color="primary" routerLink="/staff/new">
            <i class="pi pi-user-plus"></i>
            Add Staff Member
          </button>
        </div>
      </div>

      <!-- Search and Filters -->
      <p-card class="filters-card">
        <p-card-content>
          <div class="filters-row">
            <div class="p-field" appearance="outline" class="search-field">
              <label>Search staff...</label>
              <input matInput
                     placeholder="Name, email, position..."
                     (input)="onSearchChange($event)">
              <i class="pi pi-search"></i>
            </div>

            <button p-button [outlined]="true">
              <i class="pi pi-filter"></i>
              Filters
            </button>

            <button p-button [outlined]="true">
              <i class="pi pi-download"></i>
              Export
            </button>
          </div>
        </ng-template>
      </p-card>

      <!-- Staff Stats -->
      <div class="stats-row">
        <p-card class="stat-card">
          <p-card-content>
            <div class="stat-content">
              <i class="pi pi-users"></i>
              <div class="stat-text">
                <h3>{{ (staffData$ | async)?.pagination?.totalCount || 0 }}</h3>
                <p>Total Staff</p>
              </div>
            </div>
          </ng-template>
        </p-card>

        <p-card class="stat-card">
          <p-card-content>
            <div class="stat-content">
              <i class="pi pi-check-circle"></i>
              <div class="stat-text">
                <h3>{{ activeStaffCount }}</h3>
                <p>Active</p>
              </div>
            </div>
          </ng-template>
        </p-card>

        <p-card class="stat-card">
          <p-card-content>
            <div class="stat-content">
              <i class="pi pi-building"></i>
              <div class="stat-text">
                <h3>{{ departmentCount }}</h3>
                <p>Departments</p>
              </div>
            </div>
          </ng-template>
        </p-card>
      </div>

      <!-- Staff Table -->
      <p-card class="table-card">
        <p-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="(staffData$ | async)?.staff || []" class="staff-table">

              <!-- Avatar & Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Staff Member</th>
                <td mat-cell *matCellDef="let staff">
                  <div class="staff-info">
                    <div class="avatar">
                      <i class="pi pi-user"></i>
                    </div>
                    <div class="info">
                      <div class="name">{{ staff.firstName }} {{ staff.lastName }}</div>
                      <div class="email">{{ staff.email }}</div>
                    </div>
                  </div>
                </td>
              </ng-container>

              <!-- Position Column -->
              <ng-container matColumnDef="position">
                <th mat-header-cell *matHeaderCellDef>Position</th>
                <td mat-cell *matCellDef="let staff">
                  <div class="position-info">
                    <div class="position">{{ staff.position }}</div>
                    <div class="department">{{ staff.department }}</div>
                  </div>
                </td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let staff">
                  <p-chip-set>
                    <p-chip [class]="'status-' + staff.status">
                      {{ staff.status | titlecase }}
                    </p-chip>
                  </div>
                </td>
              </ng-container>

              <!-- Employment Type Column -->
              <ng-container matColumnDef="employmentType">
                <th mat-header-cell *matHeaderCellDef>Type</th>
                <td mat-cell *matCellDef="let staff">
                  {{ staff.employmentType | titlecase }}
                </td>
              </ng-container>

              <!-- Hire Date Column -->
              <ng-container matColumnDef="hireDate">
                <th mat-header-cell *matHeaderCellDef>Hire Date</th>
                <td mat-cell *matCellDef="let staff">
                  {{ staff.hireDate | date:'MMM d, y' }}
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let staff">
                  <div class="actions">
                    <button p-button [text]="true" [routerLink]="['/staff', staff.id]" pTooltip="View Details">
                      <i class="pi pi-eye"></i>
                    </button>
                    <button p-button [text]="true" [routerLink]="['/staff', staff.id, 'edit']" pTooltip="Edit">
                      <i class="pi pi-pencil"></i>
                    </button>
                    <button p-button [text]="true" pTooltip="More Options">
                      <i class="pi pi-ellipsis-v"></i>
                    </button>
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"
                  class="staff-row"
                  [routerLink]="['/staff', row.id]"></tr>
            </table>
          </div>

          <!-- Pagination -->
          <div class="pagination" *ngIf="(staffData$ | async)?.pagination as pagination">
            <div class="pagination-info">
              Showing {{ ((pagination.page - 1) * pagination.pageSize) + 1 }} -
              {{ Math.min(pagination.page * pagination.pageSize, pagination.totalCount) }}
              of {{ pagination.totalCount }} staff members
            </div>
            <div class="pagination-controls">
              <button p-button [text]="true" [disabled]="pagination.page === 1">
                <i class="pi pi-chevron-left"></i>
              </button>
              <span>{{ pagination.page }} of {{ pagination.totalPages }}</span>
              <button p-button [text]="true" [disabled]="pagination.page === pagination.totalPages">
                <i class="pi pi-chevron-right"></i>
              </button>
            </div>
          </div>
        </ng-template>
      </p-card>
    </div>
  `,
  styleUrls: ['./staff-list.component.scss']
})
export class StaffListComponent implements OnInit {
  staffData$: Observable<StaffListResponse>;
  displayedColumns = ['name', 'position', 'status', 'employmentType', 'hireDate', 'actions'];

  activeStaffCount = 0;
  departmentCount = 0;
  Math = Math;

  constructor(private staffService: StaffService) {
    this.staffData$ = this.staffService.paginatedStaff$;
  }

  ngOnInit(): void {
    // Load initial data and stats
    this.staffService.getActiveStaff().subscribe(activeStaff => {
      this.activeStaffCount = activeStaff.length;
    });

    this.staffService.getDepartments().subscribe(departments => {
      this.departmentCount = departments.length;
    });
  }

  onSearchChange(event: any): void {
    const searchTerm = event.target.value;
    this.staffService.setFilter({ search: searchTerm });
  }
}

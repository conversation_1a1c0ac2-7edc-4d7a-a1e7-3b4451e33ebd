import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatChipsModule } from '@angular/material/chips';
import { MatBadgeModule } from '@angular/material/badge';
import { RouterModule } from '@angular/router';
import { Observable } from 'rxjs';

import { StaffService } from '../services/staff.service';
import { StaffMember, StaffListResponse } from '../models/staff.model';

@Component({
  selector: 'app-staff-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatInputModule,
    MatFormFieldModule,
    MatChipsModule,
    MatBadgeModule
  ],
  template: `
    <div class="staff-list-container">
      <!-- Header -->
      <div class="staff-header">
        <div class="header-left">
          <h1>
            <mat-icon>people</mat-icon>
            Staff Directory
          </h1>
          <p>Manage your team members and their information</p>
        </div>
        <div class="header-actions">
          <button mat-raised-button color="primary" routerLink="/staff/new">
            <mat-icon>person_add</mat-icon>
            Add Staff Member
          </button>
        </div>
      </div>

      <!-- Search and Filters -->
      <mat-card class="filters-card">
        <mat-card-content>
          <div class="filters-row">
            <mat-form-field appearance="outline" class="search-field">
              <mat-label>Search staff...</mat-label>
              <input matInput
                     placeholder="Name, email, position..."
                     (input)="onSearchChange($event)">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>

            <button mat-stroked-button>
              <mat-icon>filter_list</mat-icon>
              Filters
            </button>

            <button mat-stroked-button>
              <mat-icon>import_export</mat-icon>
              Export
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Staff Stats -->
      <div class="stats-row">
        <mat-card class="stat-card">
          <mat-card-content>
            <div class="stat-content">
              <mat-icon class="stat-icon active">people</mat-icon>
              <div class="stat-text">
                <h3>{{ (staffData$ | async)?.pagination?.totalCount || 0 }}</h3>
                <p>Total Staff</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="stat-card">
          <mat-card-content>
            <div class="stat-content">
              <mat-icon class="stat-icon online">check_circle</mat-icon>
              <div class="stat-text">
                <h3>{{ activeStaffCount }}</h3>
                <p>Active</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="stat-card">
          <mat-card-content>
            <div class="stat-content">
              <mat-icon class="stat-icon departments">business</mat-icon>
              <div class="stat-text">
                <h3>{{ departmentCount }}</h3>
                <p>Departments</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Staff Table -->
      <mat-card class="table-card">
        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="(staffData$ | async)?.staff || []" class="staff-table">

              <!-- Avatar & Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Staff Member</th>
                <td mat-cell *matCellDef="let staff">
                  <div class="staff-info">
                    <div class="avatar">
                      <mat-icon>person</mat-icon>
                    </div>
                    <div class="info">
                      <div class="name">{{ staff.firstName }} {{ staff.lastName }}</div>
                      <div class="email">{{ staff.email }}</div>
                    </div>
                  </div>
                </td>
              </ng-container>

              <!-- Position Column -->
              <ng-container matColumnDef="position">
                <th mat-header-cell *matHeaderCellDef>Position</th>
                <td mat-cell *matCellDef="let staff">
                  <div class="position-info">
                    <div class="position">{{ staff.position }}</div>
                    <div class="department">{{ staff.department }}</div>
                  </div>
                </td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let staff">
                  <mat-chip-set>
                    <mat-chip [class]="'status-' + staff.status">
                      {{ staff.status | titlecase }}
                    </mat-chip>
                  </mat-chip-set>
                </td>
              </ng-container>

              <!-- Employment Type Column -->
              <ng-container matColumnDef="employmentType">
                <th mat-header-cell *matHeaderCellDef>Type</th>
                <td mat-cell *matCellDef="let staff">
                  {{ staff.employmentType | titlecase }}
                </td>
              </ng-container>

              <!-- Hire Date Column -->
              <ng-container matColumnDef="hireDate">
                <th mat-header-cell *matHeaderCellDef>Hire Date</th>
                <td mat-cell *matCellDef="let staff">
                  {{ staff.hireDate | date:'MMM d, y' }}
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let staff">
                  <div class="actions">
                    <button mat-icon-button [routerLink]="['/staff', staff.id]" matTooltip="View Details">
                      <mat-icon>visibility</mat-icon>
                    </button>
                    <button mat-icon-button [routerLink]="['/staff', staff.id, 'edit']" matTooltip="Edit">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button matTooltip="More Options">
                      <mat-icon>more_vert</mat-icon>
                    </button>
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"
                  class="staff-row"
                  [routerLink]="['/staff', row.id]"></tr>
            </table>
          </div>

          <!-- Pagination -->
          <div class="pagination" *ngIf="(staffData$ | async)?.pagination as pagination">
            <div class="pagination-info">
              Showing {{ ((pagination.page - 1) * pagination.pageSize) + 1 }} -
              {{ Math.min(pagination.page * pagination.pageSize, pagination.totalCount) }}
              of {{ pagination.totalCount }} staff members
            </div>
            <div class="pagination-controls">
              <button mat-icon-button [disabled]="pagination.page === 1">
                <mat-icon>chevron_left</mat-icon>
              </button>
              <span>{{ pagination.page }} of {{ pagination.totalPages }}</span>
              <button mat-icon-button [disabled]="pagination.page === pagination.totalPages">
                <mat-icon>chevron_right</mat-icon>
              </button>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styleUrls: ['./staff-list.component.scss']
})
export class StaffListComponent implements OnInit {
  staffData$: Observable<StaffListResponse>;
  displayedColumns = ['name', 'position', 'status', 'employmentType', 'hireDate', 'actions'];

  activeStaffCount = 0;
  departmentCount = 0;
  Math = Math;

  constructor(private staffService: StaffService) {
    this.staffData$ = this.staffService.paginatedStaff$;
  }

  ngOnInit(): void {
    // Load initial data and stats
    this.staffService.getActiveStaff().subscribe(activeStaff => {
      this.activeStaffCount = activeStaff.length;
    });

    this.staffService.getDepartments().subscribe(departments => {
      this.departmentCount = departments.length;
    });
  }

  onSearchChange(event: any): void {
    const searchTerm = event.target.value;
    this.staffService.setFilter({ search: searchTerm });
  }
}

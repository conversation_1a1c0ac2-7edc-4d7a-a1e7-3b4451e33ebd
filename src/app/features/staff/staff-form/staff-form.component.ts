import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { StaffService } from '../services/staff.service';
import { StaffMember } from '../models/staff.model';
import { AuthService, UserProfile } from '../../../core/auth/auth.service';
import { Observable, of, take } from 'rxjs';

@Component({
  selector: 'app-staff-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="staff-form-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>{{ isEditMode ? 'edit' : 'person_add' }}</mat-icon>
            {{ isEditMode ? 'Edit Staff Profile' : 'Add New Staff Member' }}
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="staffForm" (ngSubmit)="onSubmit()" *ngIf="!loading">

            <!-- Basic Information Section -->
            <div class="form-section">
              <h3>Basic Information</h3>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>First Name</mat-label>
                  <input matInput formControlName="firstName" required>
                  <mat-error *ngIf="staffForm.get('firstName')?.hasError('required')">
                    First name is required
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Last Name</mat-label>
                  <input matInput formControlName="lastName" required>
                  <mat-error *ngIf="staffForm.get('lastName')?.hasError('required')">
                    Last name is required
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Email</mat-label>
                  <input matInput type="email" formControlName="email" required>
                  <mat-error *ngIf="staffForm.get('email')?.hasError('required')">
                    Email is required
                  </mat-error>
                  <mat-error *ngIf="staffForm.get('email')?.hasError('email')">
                    Please enter a valid email
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Phone</mat-label>
                  <input matInput formControlName="phone" required>
                  <mat-error *ngIf="staffForm.get('phone')?.hasError('required')">
                    Phone is required
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Position</mat-label>
                  <input matInput formControlName="position" required>
                  <mat-error *ngIf="staffForm.get('position')?.hasError('required')">
                    Position is required
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Department</mat-label>
                  <input matInput formControlName="department" required>
                  <mat-error *ngIf="staffForm.get('department')?.hasError('required')">
                    Department is required
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Employment Type</mat-label>
                  <mat-select formControlName="employmentType" required>
                    <mat-option value="full-time">Full Time</mat-option>
                    <mat-option value="part-time">Part Time</mat-option>
                    <mat-option value="contract">Contract</mat-option>
                    <mat-option value="intern">Intern</mat-option>
                  </mat-select>
                  <mat-error *ngIf="staffForm.get('employmentType')?.hasError('required')">
                    Employment type is required
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Status</mat-label>
                  <mat-select formControlName="status" required>
                    <mat-option value="active">Active</mat-option>
                    <mat-option value="inactive">Inactive</mat-option>
                    <mat-option value="on-leave">On Leave</mat-option>
                  </mat-select>
                  <mat-error *ngIf="staffForm.get('status')?.hasError('required')">
                    Status is required
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Hire Date</mat-label>
                  <input matInput [matDatepicker]="hireDatePicker" formControlName="hireDate" required>
                  <mat-datepicker-toggle matIconSuffix [for]="hireDatePicker"></mat-datepicker-toggle>
                  <mat-datepicker #hireDatePicker></mat-datepicker>
                  <mat-error *ngIf="staffForm.get('hireDate')?.hasError('required')">
                    Hire date is required
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Date of Birth</mat-label>
                  <input matInput [matDatepicker]="dobPicker" formControlName="dateOfBirth">
                  <mat-datepicker-toggle matIconSuffix [for]="dobPicker"></mat-datepicker-toggle>
                  <mat-datepicker #dobPicker></mat-datepicker>
                </mat-form-field>
              </div>
            </div>

            <!-- Bio Section -->
            <div class="form-section">
              <h3>Bio</h3>
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Bio</mat-label>
                <textarea matInput formControlName="bio" rows="4"
                         placeholder="Tell us about yourself..."></textarea>
              </mat-form-field>
            </div>

          </form>

          <!-- Loading Spinner -->
          <div *ngIf="loading" class="loading-container">
            <mat-spinner></mat-spinner>
            <p>Loading staff information...</p>
          </div>
        </mat-card-content>

        <mat-card-actions align="end">
          <button mat-button type="button" (click)="onCancel()">
            <mat-icon>cancel</mat-icon>
            Cancel
          </button>
          <button mat-raised-button color="primary"
                  [disabled]="staffForm.invalid || loading"
                  (click)="onSubmit()">
            <mat-icon>save</mat-icon>
            {{ isEditMode ? 'Update' : 'Create' }}
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .staff-form-container {
      padding: 24px;
      max-width: 800px;
      margin: 0 auto;
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .form-section {
      margin-bottom: 32px;
    }

    .form-section h3 {
      margin: 0 0 16px 0;
      color: rgba(0, 0, 0, 0.87);
      font-weight: 500;
    }

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
    }

    .form-row mat-form-field {
      flex: 1;
    }

    .full-width {
      width: 100%;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 40px;
      gap: 16px;
    }

    mat-card-actions {
      padding: 16px 24px;
      gap: 8px;
    }

    @media (max-width: 768px) {
      .form-row {
        flex-direction: column;
        gap: 8px;
      }

      .staff-form-container {
        padding: 16px;
      }
    }
  `]
})
export class StaffFormComponent implements OnInit {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private staffService = inject(StaffService);
  private authService = inject(AuthService);
  private snackBar = inject(MatSnackBar);

  staffForm!: FormGroup;
  loading = false;
  isEditMode = false;
  staffId: string | null = null;

  ngOnInit(): void {
    this.initializeForm();
    this.checkEditMode();
  }

  private initializeForm(): void {
    this.staffForm = this.fb.group({
      firstName: ['', [Validators.required]],
      lastName: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required]],
      position: ['', [Validators.required]],
      department: ['', [Validators.required]],
      employmentType: ['full-time', [Validators.required]],
      status: ['active', [Validators.required]],
      hireDate: ['', [Validators.required]],
      dateOfBirth: [''],
      bio: ['']
    });
  }

  private checkEditMode(): void {
    this.staffId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.staffId;

    if (this.isEditMode && this.staffId) {
      this.loadStaffMember(this.staffId);
    }
  }

  private loadStaffMember(id: string): void {
    this.loading = true;

    // Load user profile using AuthService (handles injection context properly)
    this.authService.userProfile$.pipe(take(1)).subscribe(profile => {
      if (profile) {
        // Convert user profile to staff member format for the form
        const staffData: Partial<StaffMember> = {
          id: profile.uid,
          firstName: (profile as any).firstName || this.extractFirstName(profile.displayName || ''),
          lastName: (profile as any).lastName || this.extractLastName(profile.displayName || ''),
          email: profile.email,
          phone: (profile as any).phone || '',
          position: (profile as any).position || '',
          department: (profile as any).department || '',
          employmentType: (profile as any).employmentType || 'full-time',
          status: (profile as any).status || 'active',
          hireDate: (profile as any).hireDate || profile.createdAt,
          dateOfBirth: (profile as any).dateOfBirth,
          bio: (profile as any).bio || ''
        };

        this.populateForm(staffData as StaffMember);
        this.loading = false;
      } else {
        // No user profile found, use basic user data
        this.loadFromUserProfile(id);
      }
    });
  }

  private loadFromUserProfile(id: string): void {
    // Get current user profile and use it for staff data
    this.authService.userProfile$.pipe(take(1)).subscribe(profile => {
      if (profile) {
        // Convert user profile to staff member format
        const staffData: Partial<StaffMember> = {
          id: id,
          firstName: this.extractFirstName(profile.displayName),
          lastName: this.extractLastName(profile.displayName),
          email: profile.email,
          phone: '', // Will need to be filled by user
          position: '', // Will need to be filled by user
          department: '', // Will need to be filled by user
          employmentType: 'full-time',
          status: 'active',
          hireDate: profile.createdAt,
          dateOfBirth: undefined,
          bio: ''
        };

        this.populateForm(staffData as StaffMember);
        this.loading = false;
      } else {
        // Fallback: redirect to login if no profile
        this.router.navigate(['/auth/login']);
        this.loading = false;
      }
    });
  }

  private extractFirstName(displayName: string): string {
    return displayName.split(' ')[0] || '';
  }

  private extractLastName(displayName: string): string {
    const parts = displayName.split(' ');
    return parts.length > 1 ? parts.slice(1).join(' ') : '';
  }

  private populateForm(staff: StaffMember): void {
    this.staffForm.patchValue({
      firstName: staff.firstName,
      lastName: staff.lastName,
      email: staff.email,
      phone: staff.phone,
      position: staff.position,
      department: staff.department,
      employmentType: staff.employmentType,
      status: staff.status,
      hireDate: staff.hireDate,
      dateOfBirth: staff.dateOfBirth,
      bio: staff.bio
    });
  }

  onSubmit(): void {
    if (this.staffForm.valid) {
      this.loading = true;
      const formData = this.staffForm.value;

      if (this.isEditMode && this.staffId) {
        this.updateStaffMember(this.staffId, formData);
      } else {
        this.createStaffMember(formData);
      }
    }
  }

  private createStaffMember(data: any): void {
    console.log('Creating staff member:', data);

    // Get current user to create staff profile
    this.authService.user$.pipe(take(1)).subscribe(user => {
      if (user) {
        const staffData = {
          ...data,
          uid: user.uid,
          email: user.email,
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: user.uid
        };

        // Save to Firestore using staff service
        this.staffService.createStaffMember(staffData).subscribe({
          next: (result) => {
            this.snackBar.open('Staff profile created successfully', 'Close', { duration: 3000 });
            this.router.navigate(['/staff']);
            this.loading = false;
          },
          error: (error) => {
            console.error('Error creating staff member:', error);
            this.snackBar.open('Error creating staff profile. Please try again.', 'Close', { duration: 5000 });
            this.loading = false;
          }
        });
      } else {
        this.snackBar.open('User not authenticated. Please login again.', 'Close', { duration: 5000 });
        this.router.navigate(['/auth/login']);
        this.loading = false;
      }
    });
  }

  private updateStaffMember(id: string, data: any): void {
    console.log('Updating staff member:', id, data);

    // Get current user to update staff profile
    this.authService.user$.pipe(take(1)).subscribe(user => {
      if (user) {
        // Prepare profile updates
        const profileUpdates = {
          displayName: `${data.firstName} ${data.lastName}`,
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          phone: data.phone,
          position: data.position,
          department: data.department,
          employmentType: data.employmentType,
          status: data.status,
          hireDate: data.hireDate,
          dateOfBirth: data.dateOfBirth,
          bio: data.bio,
          updatedAt: new Date(),
          updatedBy: user.uid
        };

        // Use AuthService to update profile (handles injection context properly)
        this.authService.updateUserProfile(user.uid, profileUpdates).subscribe({
          next: () => {
            console.log('✅ User profile updated successfully');
            this.snackBar.open('Staff profile updated successfully', 'Close', { duration: 3000 });
            this.router.navigate(['/settings']);
            this.loading = false;
          },
          error: (error) => {
            console.error('❌ Error updating staff profile:', error);
            this.snackBar.open('Error updating staff profile. Please try again.', 'Close', { duration: 5000 });
            this.loading = false;
          }
        });
      } else {
        this.snackBar.open('User not authenticated. Please login again.', 'Close', { duration: 5000 });
        this.router.navigate(['/auth/login']);
        this.loading = false;
      }
    });
  }

  onCancel(): void {
    if (this.isEditMode && this.staffId) {
      this.router.navigate(['/staff/profile', this.staffId]);
    } else {
      this.router.navigate(['/staff']);
    }
  }
}

import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-staff-hub',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule],
  template: `
    <div class="staff-hub-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>hub</mat-icon>
            Staff Hub (Employee Portal)
          </mat-card-title>
          <mat-card-subtitle>Employee self-service portal</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p>Staff Hub coming soon! This will include:</p>
          <ul>
            <li>Employee dashboard</li>
            <li>Time tracking</li>
            <li>Schedule viewing</li>
            <li>Time-off requests</li>
            <li>Personal information management</li>
          </ul>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .staff-hub-container {
      padding: 24px;
    }
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  `]
})
export class StaffHubComponent {}

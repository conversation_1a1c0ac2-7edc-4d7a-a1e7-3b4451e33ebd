import { Component, OnInit, inject } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AuthService } from '../../core/auth/auth.service';
import { UserDataMigrationService } from '../../core/services/user-data-migration.service';
import { switchMap, take } from 'rxjs/operators';

/**
 * Staff Profile Redirect Component
 *
 * This component helps users navigate to their own staff profile
 * and handles data initialization if needed.
 */
@Component({
  selector: 'app-staff-profile-redirect',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    ButtonModule
    ProgressSpinnerModule
  ],
  template: `
    <div class="profile-redirect-container">
      <p-card class="profile-card">
        <p-card-header>
          <i class="pi pi-user-plus"></i>
          <p-card-title>Profile Setup</ng-template>
          <p-card-subtitle>Set up your staff profile to access all features</ng-template>
        </ng-template>

        <p-card-content>
          <div *ngIf="isLoading" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
            <p>Setting up your profile...</p>
          </div>

          <div *ngIf="!isLoading && !hasProfile" class="no-profile-container">
            <i class="pi pi-user-plus"></i>
            <h3>Profile Setup Required</h3>
            <p>It looks like your staff profile hasn't been set up yet. Click the button below to initialize your profile and sample data.</p>

            <div class="action-buttons">
              <button p-button color="primary" (click)="initializeProfile()">
                <i class="pi pi-user-plus"></i>
                Set Up My Profile
              </button>

              <button p-button [outlined]="true" (click)="createSampleData()" [disabled]="!profileInitialized">
                <mat-icon>data_object</mat-icon>
                Add Sample Data
              </button>
            </div>
          </div>

          <div *ngIf="!isLoading && hasProfile" class="profile-ready-container">
            <i class="pi pi-check-circle"></i>
            <h3>Profile Ready!</h3>
            <p>Your staff profile has been set up successfully. You can now access all StaffManager features.</p>

            <div class="action-buttons">
              <button p-button color="primary" (click)="viewProfile()">
                <i class="pi pi-user"></i>
                View My Profile
              </button>

              <button p-button [outlined]="true" (click)="navigateTo('/dashboard')">
                <i class="pi pi-th-large"></i>
                Go to Dashboard
              </button>
            </div>
          </div>
        </ng-template>
      </p-card>

      <!-- Quick Actions Card -->
      <p-card class="quick-actions-card" *ngIf="hasProfile">
        <p-card-header>
          <mat-icon mat-card-avatar>flash_on</mat-icon>
          <p-card-title>Quick Actions</ng-template>
        </ng-template>

        <p-card-content>
          <div class="quick-actions-grid">
            <button p-button [outlined]="true" (click)="navigateTo('/tasks')">
              <mat-icon>assignment</mat-icon>
              My Tasks
            </button>

            <button p-button [outlined]="true" (click)="navigateTo('/goals')">
              <mat-icon>flag</mat-icon>
              My Goals
            </button>

            <button p-button [outlined]="true" (click)="navigateTo('/calendar')">
              <i class="pi pi-calendar"></i>
              My Schedule
            </button>

            <button p-button [outlined]="true" (click)="navigateTo('/time')">
              <i class="pi pi-clock"></i>
              Time Tracking
            </button>
          </div>
        </ng-template>
      </p-card>
    </div>
  `,
  styles: [`
    .profile-redirect-container {
      padding: 24px;
      max-width: 800px;
      margin: 0 auto;
    }

    .profile-card, .quick-actions-card {
      margin-bottom: 24px;
    }

    .loading-container {
      text-align: center;
      padding: 40px 20px;
    }

    .loading-container p {
      margin-top: 16px;
      color: rgba(0, 0, 0, 0.6);
    }

    .no-profile-container, .profile-ready-container {
      text-align: center;
      padding: 40px 20px;
    }

    .large-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      color: rgba(0, 0, 0, 0.4);
      margin-bottom: 16px;
    }

    .large-icon.success {
      color: #4caf50;
    }

    .action-buttons {
      display: flex;
      gap: 16px;
      justify-content: center;
      margin-top: 24px;
      flex-wrap: wrap;
    }

    .quick-actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;
      margin-top: 16px;
    }

    .quick-actions-grid button {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px;
      min-height: 80px;
    }

    .quick-actions-grid button mat-icon {
      margin-bottom: 8px;
    }

    h3 {
      margin: 16px 0 8px 0;
      color: rgba(0, 0, 0, 0.87);
    }

    p {
      color: rgba(0, 0, 0, 0.6);
      line-height: 1.5;
    }
  `]
})
export class StaffProfileRedirectComponent implements OnInit {
  private authService = inject(AuthService);
  private migrationService = inject(UserDataMigrationService);
  private router = inject(Router);
  private snackBar = inject(MatSnackBar);

  isLoading = true;
  hasProfile = false;
  profileInitialized = false;

  ngOnInit(): void {
    this.checkProfileStatus();
  }

  private checkProfileStatus(): void {
    this.authService.userProfile$.pipe(take(1)).subscribe(profile => {
      if (profile) {
        this.hasProfile = !!(profile.staffId && profile.primaryBusinessId);
        this.profileInitialized = this.hasProfile;
      }
      this.isLoading = false;
    });
  }

  initializeProfile(): void {
    this.isLoading = true;

    this.authService.userProfile$.pipe(
      take(1),
      switchMap(profile => {
        if (!profile) {
          throw new Error('No user profile found');
        }
        return this.migrationService.checkAndMigrateUserData();
      })
    ).subscribe({
      next: (success) => {
        if (success) {
          this.snackBar.open('Profile initialized successfully!', 'Close', { duration: 3000 });
          this.hasProfile = true;
          this.profileInitialized = true;

          // Refresh user profile to get updated staffId
          this.authService.refreshUserProfile().subscribe({
            next: (refreshedProfile) => {
              console.log('✅ User profile refreshed:', refreshedProfile);
              // Automatically navigate to the user's profile after refresh
              setTimeout(() => {
                this.viewProfile();
              }, 1000);
            },
            error: (error) => {
              console.error('Error refreshing user profile:', error);
              // Still try to navigate even if refresh fails
              setTimeout(() => {
                this.viewProfile();
              }, 2000);
            }
          });
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error initializing profile:', error);
        const errorMessage = error?.message || 'Unknown error occurred';
        this.snackBar.open(`Error initializing profile: ${errorMessage}`, 'Close', { duration: 8000 });
        this.isLoading = false;
      }
    });
  }

  createSampleData(): void {
    this.isLoading = true;

    this.authService.userProfile$.pipe(
      take(1),
      switchMap(profile => {
        if (!profile) {
          throw new Error('No user profile found');
        }
        return this.migrationService.createSampleData(profile);
      })
    ).subscribe({
      next: (success) => {
        if (success) {
          this.snackBar.open('Sample data created successfully!', 'Close', { duration: 3000 });
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error creating sample data:', error);
        const errorMessage = error?.message || 'Unknown error occurred';
        this.snackBar.open(`Error creating sample data: ${errorMessage}`, 'Close', { duration: 8000 });
        this.isLoading = false;
      }
    });
  }

  viewProfile(): void {
    this.authService.userProfile$.pipe(take(1)).subscribe(profile => {
      if (profile?.staffId) {
        this.router.navigate(['/staff/profile', profile.staffId]);
      }
    });
  }

  editProfile(): void {
    this.authService.userProfile$.pipe(take(1)).subscribe(profile => {
      if (profile?.staffId) {
        this.router.navigate(['/staff/edit', profile.staffId]);
      }
    });
  }

  navigateTo(route: string): void {
    this.router.navigate([route]);
  }
}

import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';

import { ButtonModule } from 'primeng/button';

import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { MatOptionModule } from '@angular/material/core';
import { CheckboxModule } from 'primeng/checkbox';
import { MatSliderModule } from '@angular/material/slider';
import { TabViewModule } from 'primeng/tabview';

import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { ChipModule } from 'primeng/chip';

export interface CustomWidgetConfig {
  id?: string;
  title: string;
  type: 'number' | 'chart' | 'list' | 'table' | 'gauge' | 'progress' | 'custom';
  size: 'small' | 'medium' | 'large' | 'xlarge';
  dataSource: {
    type: 'static' | 'api' | 'database' | 'realtime';
    endpoint?: string;
    query?: string;
    refreshInterval?: number;
  };
  visualization: {
    chartType?: 'line' | 'bar' | 'pie' | 'doughnut' | 'area' | 'scatter';
    colors?: string[];
    showLegend?: boolean;
    showGrid?: boolean;
    animation?: boolean;
  };
  styling: {
    backgroundColor?: string;
    textColor?: string;
    borderRadius?: number;
    padding?: number;
    fontSize?: number;
  };
  filters?: {
    dateRange?: boolean;
    businessFilter?: boolean;
    customFilters?: string[];
  };
  permissions?: {
    viewRoles?: string[];
    editRoles?: string[];
  };
}

@Component({
  selector: 'app-custom-widget-builder',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule
    ButtonModule
    InputTextModule,
    DropdownModule,
    MatOptionModule,
    CheckboxModule,
    MatSliderModule,
    TabViewModule
    CardModule,
    DividerModule,
    ChipModule
  ],
  template: `
    <div class="widget-builder-dialog">
      <h2 mat-dialog-title>
        <mat-icon>widgets</mat-icon>
        Custom Widget Builder
      </h2>
      
      <div mat-dialog-content class="dialog-content">
        <form [formGroup]="widgetForm" class="widget-form">
          <p-tabView>
            <!-- Basic Configuration Tab -->
            <p-tabPanel label="Basic">
              <div class="tab-content">
                <div class="p-field" appearance="outline" class="full-width">
                  <label>Widget Title</label>
                  <input matInput formControlName="title" placeholder="Enter widget title">
                  <mat-error *ngIf="widgetForm.get('title')?.hasError('required')">
                    Title is required
                  </mat-error>
                </div>

                <div class="p-field" appearance="outline" class="full-width">
                  <label>Widget Type</label>
                  <p-dropdown formControlName="type">
                    <p-option value="number">Number Display</p-option>
                    <p-option value="chart">Chart</p-option>
                    <p-option value="list">List</p-option>
                    <p-option value="table">Table</p-option>
                    <p-option value="gauge">Gauge</p-option>
                    <p-option value="progress">Progress Bar</p-option>
                    <p-option value="custom">Custom HTML</p-option>
                  </p-dropdown>
                </div>

                <div class="p-field" appearance="outline" class="full-width">
                  <label>Widget Size</label>
                  <p-dropdown formControlName="size">
                    <p-option value="small">Small (1x1)</p-option>
                    <p-option value="medium">Medium (2x1)</p-option>
                    <p-option value="large">Large (2x2)</p-option>
                    <p-option value="xlarge">Extra Large (3x2)</p-option>
                  </p-dropdown>
                </div>
              </div>
            </p-tabPanel>

            <!-- Data Source Tab -->
            <p-tabPanel label="Data Source">
              <div class="tab-content" formGroupName="dataSource">
                <div class="p-field" appearance="outline" class="full-width">
                  <label>Data Source Type</label>
                  <p-dropdown formControlName="type">
                    <p-option value="static">Static Data</p-option>
                    <p-option value="api">REST API</p-option>
                    <p-option value="database">Database Query</p-option>
                    <p-option value="realtime">Real-time Stream</p-option>
                  </p-dropdown>
                </div>

                <div class="p-field" appearance="outline" class="full-width" 
                               *ngIf="widgetForm.get('dataSource.type')?.value !== 'static'">
                  <label>Endpoint/Query</label>
                  <textarea matInput formControlName="endpoint" 
                           placeholder="Enter API endpoint or database query"
                           rows="3"></textarea>
                </div>

                <div class="p-field" appearance="outline" class="full-width">
                  <label>Refresh Interval (seconds)</label>
                  <input matInput type="number" formControlName="refreshInterval" 
                         placeholder="60" min="5" max="3600">
                </div>
              </div>
            </p-tabPanel>

            <!-- Visualization Tab -->
            <p-tabPanel label="Visualization" *ngIf="widgetForm.get('type')?.value === 'chart'">
              <div class="tab-content" formGroupName="visualization">
                <div class="p-field" appearance="outline" class="full-width">
                  <label>Chart Type</label>
                  <p-dropdown formControlName="chartType">
                    <p-option value="line">Line Chart</p-option>
                    <p-option value="bar">Bar Chart</p-option>
                    <p-option value="pie">Pie Chart</p-option>
                    <p-option value="doughnut">Doughnut Chart</p-option>
                    <p-option value="area">Area Chart</p-option>
                    <p-option value="scatter">Scatter Plot</p-option>
                  </p-dropdown>
                </div>

                <div class="checkbox-group">
                  <p-checkbox formControlName="showLegend">Show Legend</p-checkbox>
                  <p-checkbox formControlName="showGrid">Show Grid</p-checkbox>
                  <p-checkbox formControlName="animation">Enable Animation</p-checkbox>
                </div>
              </div>
            </p-tabPanel>

            <!-- Styling Tab -->
            <p-tabPanel label="Styling">
              <div class="tab-content" formGroupName="styling">
                <div class="p-field" appearance="outline" class="half-width">
                  <label>Background Color</label>
                  <input matInput type="color" formControlName="backgroundColor">
                </div>

                <div class="p-field" appearance="outline" class="half-width">
                  <label>Text Color</label>
                  <input matInput type="color" formControlName="textColor">
                </div>

                <div class="slider-group">
                  <label>Border Radius: {{widgetForm.get('styling.borderRadius')?.value}}px</label>
                  <mat-slider min="0" max="20" step="1" formControlName="borderRadius"></mat-slider>
                </div>

                <div class="slider-group">
                  <label>Padding: {{widgetForm.get('styling.padding')?.value}}px</label>
                  <mat-slider min="8" max="32" step="2" formControlName="padding"></mat-slider>
                </div>

                <div class="slider-group">
                  <label>Font Size: {{widgetForm.get('styling.fontSize')?.value}}px</label>
                  <mat-slider min="10" max="24" step="1" formControlName="fontSize"></mat-slider>
                </div>
              </div>
            </p-tabPanel>

            <!-- Filters Tab -->
            <p-tabPanel label="Filters">
              <div class="tab-content" formGroupName="filters">
                <div class="checkbox-group">
                  <p-checkbox formControlName="dateRange">Date Range Filter</p-checkbox>
                  <p-checkbox formControlName="businessFilter">Business Filter</p-checkbox>
                </div>
              </div>
            </p-tabPanel>
          </p-tabView>
        </form>

        <!-- Preview Section -->
        <p-card class="preview-card">
          <p-card-header>
            <p-card-title>Preview</ng-template>
          </ng-template>
          <p-card-content>
            <div class="widget-preview" [ngStyle]="getPreviewStyles()">
              <h3>{{widgetForm.get('title')?.value || 'Widget Title'}}</h3>
              <div class="preview-content">
                <ng-container [ngSwitch]="widgetForm.get('type')?.value">
                  <div *ngSwitchCase="'number'" class="number-preview">
                    <span class="number">42</span>
                    <span class="label">Sample Value</span>
                  </div>
                  <div *ngSwitchCase="'chart'" class="chart-preview">
                    📊 {{widgetForm.get('visualization.chartType')?.value || 'Chart'}} Preview
                  </div>
                  <div *ngSwitchDefault class="default-preview">
                    {{widgetForm.get('type')?.value || 'Widget'}} Content
                  </div>
                </ng-container>
              </div>
            </div>
          </ng-template>
        </p-card>
      </div>

      <div mat-dialog-actions align="end" class="dialog-actions">
        <button mat-button (click)="onCancel()">Cancel</button>
        <button p-button color="primary" 
                [disabled]="widgetForm.invalid"
                (click)="onSave()">
          Create Widget
        </button>
      </div>
    </div>
  `,
  styleUrls: ['./custom-widget-builder.component.scss']
})
export class CustomWidgetBuilderComponent {
  widgetForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    public dialogRef: MatDialogRef<CustomWidgetBuilderComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { config?: CustomWidgetConfig }
  ) {
    this.widgetForm = this.createForm();
    
    if (data?.config) {
      this.widgetForm.patchValue(data.config);
    }
  }

  private createForm(): FormGroup {
    return this.fb.group({
      title: ['', Validators.required],
      type: ['number', Validators.required],
      size: ['medium', Validators.required],
      dataSource: this.fb.group({
        type: ['static'],
        endpoint: [''],
        refreshInterval: [60]
      }),
      visualization: this.fb.group({
        chartType: ['line'],
        showLegend: [true],
        showGrid: [true],
        animation: [true]
      }),
      styling: this.fb.group({
        backgroundColor: ['#ffffff'],
        textColor: ['#333333'],
        borderRadius: [8],
        padding: [16],
        fontSize: [14]
      }),
      filters: this.fb.group({
        dateRange: [false],
        businessFilter: [false]
      })
    });
  }

  getPreviewStyles() {
    const styling = this.widgetForm.get('styling')?.value;
    return {
      'background-color': styling?.backgroundColor || '#ffffff',
      'color': styling?.textColor || '#333333',
      'border-radius': `${styling?.borderRadius || 8}px`,
      'padding': `${styling?.padding || 16}px`,
      'font-size': `${styling?.fontSize || 14}px`
    };
  }

  onSave() {
    if (this.widgetForm.valid) {
      const config: CustomWidgetConfig = {
        ...this.widgetForm.value,
        id: this.data?.config?.id || `custom-${Date.now()}`
      };
      this.dialogRef.close(config);
    }
  }

  onCancel() {
    this.dialogRef.close();
  }
}

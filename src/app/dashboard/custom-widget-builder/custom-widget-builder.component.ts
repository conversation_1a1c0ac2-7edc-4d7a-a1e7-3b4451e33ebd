import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSliderModule } from '@angular/material/slider';
import { MatTabsModule } from '@angular/material/tabs';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';

export interface CustomWidgetConfig {
  id?: string;
  title: string;
  type: 'number' | 'chart' | 'list' | 'table' | 'gauge' | 'progress' | 'custom';
  size: 'small' | 'medium' | 'large' | 'xlarge';
  dataSource: {
    type: 'static' | 'api' | 'database' | 'realtime';
    endpoint?: string;
    query?: string;
    refreshInterval?: number;
  };
  visualization: {
    chartType?: 'line' | 'bar' | 'pie' | 'doughnut' | 'area' | 'scatter';
    colors?: string[];
    showLegend?: boolean;
    showGrid?: boolean;
    animation?: boolean;
  };
  styling: {
    backgroundColor?: string;
    textColor?: string;
    borderRadius?: number;
    padding?: number;
    fontSize?: number;
  };
  filters?: {
    dateRange?: boolean;
    businessFilter?: boolean;
    customFilters?: string[];
  };
  permissions?: {
    viewRoles?: string[];
    editRoles?: string[];
  };
}

@Component({
  selector: 'app-custom-widget-builder',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatOptionModule,
    MatCheckboxModule,
    MatSliderModule,
    MatTabsModule,
    MatIconModule,
    MatCardModule,
    MatDividerModule,
    MatChipsModule
  ],
  template: `
    <div class="widget-builder-dialog">
      <h2 mat-dialog-title>
        <mat-icon>widgets</mat-icon>
        Custom Widget Builder
      </h2>
      
      <div mat-dialog-content class="dialog-content">
        <form [formGroup]="widgetForm" class="widget-form">
          <mat-tab-group>
            <!-- Basic Configuration Tab -->
            <mat-tab label="Basic">
              <div class="tab-content">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Widget Title</mat-label>
                  <input matInput formControlName="title" placeholder="Enter widget title">
                  <mat-error *ngIf="widgetForm.get('title')?.hasError('required')">
                    Title is required
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Widget Type</mat-label>
                  <mat-select formControlName="type">
                    <mat-option value="number">Number Display</mat-option>
                    <mat-option value="chart">Chart</mat-option>
                    <mat-option value="list">List</mat-option>
                    <mat-option value="table">Table</mat-option>
                    <mat-option value="gauge">Gauge</mat-option>
                    <mat-option value="progress">Progress Bar</mat-option>
                    <mat-option value="custom">Custom HTML</mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Widget Size</mat-label>
                  <mat-select formControlName="size">
                    <mat-option value="small">Small (1x1)</mat-option>
                    <mat-option value="medium">Medium (2x1)</mat-option>
                    <mat-option value="large">Large (2x2)</mat-option>
                    <mat-option value="xlarge">Extra Large (3x2)</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </mat-tab>

            <!-- Data Source Tab -->
            <mat-tab label="Data Source">
              <div class="tab-content" formGroupName="dataSource">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Data Source Type</mat-label>
                  <mat-select formControlName="type">
                    <mat-option value="static">Static Data</mat-option>
                    <mat-option value="api">REST API</mat-option>
                    <mat-option value="database">Database Query</mat-option>
                    <mat-option value="realtime">Real-time Stream</mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width" 
                               *ngIf="widgetForm.get('dataSource.type')?.value !== 'static'">
                  <mat-label>Endpoint/Query</mat-label>
                  <textarea matInput formControlName="endpoint" 
                           placeholder="Enter API endpoint or database query"
                           rows="3"></textarea>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Refresh Interval (seconds)</mat-label>
                  <input matInput type="number" formControlName="refreshInterval" 
                         placeholder="60" min="5" max="3600">
                </mat-form-field>
              </div>
            </mat-tab>

            <!-- Visualization Tab -->
            <mat-tab label="Visualization" *ngIf="widgetForm.get('type')?.value === 'chart'">
              <div class="tab-content" formGroupName="visualization">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Chart Type</mat-label>
                  <mat-select formControlName="chartType">
                    <mat-option value="line">Line Chart</mat-option>
                    <mat-option value="bar">Bar Chart</mat-option>
                    <mat-option value="pie">Pie Chart</mat-option>
                    <mat-option value="doughnut">Doughnut Chart</mat-option>
                    <mat-option value="area">Area Chart</mat-option>
                    <mat-option value="scatter">Scatter Plot</mat-option>
                  </mat-select>
                </mat-form-field>

                <div class="checkbox-group">
                  <mat-checkbox formControlName="showLegend">Show Legend</mat-checkbox>
                  <mat-checkbox formControlName="showGrid">Show Grid</mat-checkbox>
                  <mat-checkbox formControlName="animation">Enable Animation</mat-checkbox>
                </div>
              </div>
            </mat-tab>

            <!-- Styling Tab -->
            <mat-tab label="Styling">
              <div class="tab-content" formGroupName="styling">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Background Color</mat-label>
                  <input matInput type="color" formControlName="backgroundColor">
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Text Color</mat-label>
                  <input matInput type="color" formControlName="textColor">
                </mat-form-field>

                <div class="slider-group">
                  <label>Border Radius: {{widgetForm.get('styling.borderRadius')?.value}}px</label>
                  <mat-slider min="0" max="20" step="1" formControlName="borderRadius"></mat-slider>
                </div>

                <div class="slider-group">
                  <label>Padding: {{widgetForm.get('styling.padding')?.value}}px</label>
                  <mat-slider min="8" max="32" step="2" formControlName="padding"></mat-slider>
                </div>

                <div class="slider-group">
                  <label>Font Size: {{widgetForm.get('styling.fontSize')?.value}}px</label>
                  <mat-slider min="10" max="24" step="1" formControlName="fontSize"></mat-slider>
                </div>
              </div>
            </mat-tab>

            <!-- Filters Tab -->
            <mat-tab label="Filters">
              <div class="tab-content" formGroupName="filters">
                <div class="checkbox-group">
                  <mat-checkbox formControlName="dateRange">Date Range Filter</mat-checkbox>
                  <mat-checkbox formControlName="businessFilter">Business Filter</mat-checkbox>
                </div>
              </div>
            </mat-tab>
          </mat-tab-group>
        </form>

        <!-- Preview Section -->
        <mat-card class="preview-card">
          <mat-card-header>
            <mat-card-title>Preview</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="widget-preview" [ngStyle]="getPreviewStyles()">
              <h3>{{widgetForm.get('title')?.value || 'Widget Title'}}</h3>
              <div class="preview-content">
                <ng-container [ngSwitch]="widgetForm.get('type')?.value">
                  <div *ngSwitchCase="'number'" class="number-preview">
                    <span class="number">42</span>
                    <span class="label">Sample Value</span>
                  </div>
                  <div *ngSwitchCase="'chart'" class="chart-preview">
                    📊 {{widgetForm.get('visualization.chartType')?.value || 'Chart'}} Preview
                  </div>
                  <div *ngSwitchDefault class="default-preview">
                    {{widgetForm.get('type')?.value || 'Widget'}} Content
                  </div>
                </ng-container>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <div mat-dialog-actions align="end" class="dialog-actions">
        <button mat-button (click)="onCancel()">Cancel</button>
        <button mat-raised-button color="primary" 
                [disabled]="widgetForm.invalid"
                (click)="onSave()">
          Create Widget
        </button>
      </div>
    </div>
  `,
  styleUrls: ['./custom-widget-builder.component.scss']
})
export class CustomWidgetBuilderComponent {
  widgetForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    public dialogRef: MatDialogRef<CustomWidgetBuilderComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { config?: CustomWidgetConfig }
  ) {
    this.widgetForm = this.createForm();
    
    if (data?.config) {
      this.widgetForm.patchValue(data.config);
    }
  }

  private createForm(): FormGroup {
    return this.fb.group({
      title: ['', Validators.required],
      type: ['number', Validators.required],
      size: ['medium', Validators.required],
      dataSource: this.fb.group({
        type: ['static'],
        endpoint: [''],
        refreshInterval: [60]
      }),
      visualization: this.fb.group({
        chartType: ['line'],
        showLegend: [true],
        showGrid: [true],
        animation: [true]
      }),
      styling: this.fb.group({
        backgroundColor: ['#ffffff'],
        textColor: ['#333333'],
        borderRadius: [8],
        padding: [16],
        fontSize: [14]
      }),
      filters: this.fb.group({
        dateRange: [false],
        businessFilter: [false]
      })
    });
  }

  getPreviewStyles() {
    const styling = this.widgetForm.get('styling')?.value;
    return {
      'background-color': styling?.backgroundColor || '#ffffff',
      'color': styling?.textColor || '#333333',
      'border-radius': `${styling?.borderRadius || 8}px`,
      'padding': `${styling?.padding || 16}px`,
      'font-size': `${styling?.fontSize || 14}px`
    };
  }

  onSave() {
    if (this.widgetForm.valid) {
      const config: CustomWidgetConfig = {
        ...this.widgetForm.value,
        id: this.data?.config?.id || `custom-${Date.now()}`
      };
      this.dialogRef.close(config);
    }
  }

  onCancel() {
    this.dialogRef.close();
  }
}

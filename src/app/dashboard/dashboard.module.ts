import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { DashboardComponent } from './dashboard.component';
import { WidgetSettingsDialogComponent } from './widget-settings-dialog.component';
// TODO: Uncomment after creating these components
// import { WidgetSelectorDialogComponent } from './widget-selector-dialog.component';
// import { CustomWidgetBuilderComponent } from './custom-widget-builder.component';

@NgModule({
  imports: [
    CommonModule,
    DragDropModule,
    MatGridListModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    MatSnackBarModule,
    DashboardComponent,
    WidgetSettingsDialogComponent
    // WidgetSelectorDialogComponent,
    // CustomWidgetBuilderComponent
  ],
  declarations: [],
  exports: []
})
export class DashboardModule {} 
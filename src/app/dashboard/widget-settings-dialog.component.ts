import { Component, Inject, Input } from '@angular/core';
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-widget-settings-dialog',
  standalone: true,
  imports: [CommonModule, FormsModule, MatDialogModule, MatButtonModule, MatFormFieldModule, MatInputModule],
  template: `
    <h2 mat-dialog-title>Widget Settings</h2>
    <div mat-dialog-content>
      <form>
        <mat-form-field appearance="fill" style="width: 100%;">
          <mat-label>Auto Refresh</mat-label>
          <input matInput type="checkbox" [(ngModel)]="settings.autoRefresh" name="autoRefresh">
        </mat-form-field>
        <mat-form-field appearance="fill" style="width: 100%;">
          <mat-label>Refresh Interval (seconds)</mat-label>
          <input matInput type="number" [(ngModel)]="settings.refreshInterval" name="refreshInterval">
        </mat-form-field>
        <mat-form-field appearance="fill" style="width: 100%;">
          <mat-label>Width</mat-label>
          <input matInput type="number" [(ngModel)]="settings.width" name="width">
        </mat-form-field>
        <mat-form-field appearance="fill" style="width: 100%;">
          <mat-label>Height</mat-label>
          <input matInput type="number" [(ngModel)]="settings.height" name="height">
        </mat-form-field>
      </form>
    </div>
    <div mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">Cancel</button>
      <button mat-raised-button color="primary" (click)="onSave()">Save</button>
    </div>
  `
})
export class WidgetSettingsDialogComponent {
  settings = {
    autoRefresh: false,
    refreshInterval: 60,
    width: 3,
    height: 4
  };

  constructor(
    public dialogRef: MatDialogRef<WidgetSettingsDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    if (data && data.settings) {
      this.settings = { ...this.settings, ...data.settings };
    }
  }

  onSave() {
    this.dialogRef.close(this.settings);
  }

  onCancel() {
    this.dialogRef.close();
  }
} 
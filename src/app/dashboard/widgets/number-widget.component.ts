import { Component, Input } from '@angular/core';
import { CardModule } from 'primeng/card';
import { CommonModule } from '@angular/common';

import { fadeInAnimation, cardHoverAnimation, countUpAnimation } from '../../core/animations/staffmanager-animations';

@Component({
  selector: 'app-number-widget',
  standalone: true,
  imports: [CardModule, CommonModule],
  animations: [fadeInAnimation, cardHoverAnimation, countUpAnimation],
  template: `
    <p-card class="number-widget shadow-md transition-normal"
              [ngClass]="widgetClasses"
              [@fadeIn]
              [@cardHover]="hoverState"
              (mouseenter)="hoverState = 'hovered'"
              (mouseleave)="hoverState = 'default'">

      <div class="widget-header" *ngIf="icon">
        <mat-icon class="widget-icon">{{ icon }}</mat-icon>
      </div>

      <div class="number-widget-content">
        <div class="number-widget-title">{{ title }}</div>
        <div class="number-widget-value" [@countUpAnimation]="value">
          {{ formattedValue }}
        </div>
        <div class="number-widget-description">{{ description }}</div>

        <div class="widget-trend" *ngIf="trend">
          <mat-icon class="trend-icon" [ngClass]="trendClass">{{ trendIcon }}</mat-icon>
          <span class="trend-text">{{ trend }}</span>
        </div>
      </div>

      <div class="widget-background-pattern"></div>
    </p-card>
  `,
  styleUrls: ['./number-widget.component.scss']
})
export class NumberWidgetComponent {
  @Input() title = '';
  @Input() value: number | string = '';
  @Input() description = '';
  @Input() color: 'primary' | 'accent' | 'warn' | string = 'primary';
  @Input() quickLook = false;
  @Input() icon?: string;
  @Input() trend?: string;
  @Input() trendDirection?: 'up' | 'down' | 'neutral' = 'neutral';

  hoverState: 'default' | 'hovered' = 'default';

  get formattedValue(): string {
    if (typeof this.value === 'number') {
      // Format numbers with commas for thousands
      return this.value.toLocaleString();
    }
    return String(this.value);
  }

  get widgetClasses(): { [key: string]: boolean } {
    return {
      'quicklook': this.quickLook,
      [`color-${this.color}`]: !!this.color
    };
  }

  get trendIcon(): string {
    switch (this.trendDirection) {
      case 'up':
        return 'trending_up';
      case 'down':
        return 'trending_down';
      default:
        return 'trending_flat';
    }
  }

  get trendClass(): string {
    switch (this.trendDirection) {
      case 'up':
        return 'trend-up';
      case 'down':
        return 'trend-down';
      default:
        return 'trend-neutral';
    }
  }
}
import { Component, Input, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { StaffManagerThemeService } from '../../core/theme/staffmanager-theme';
import { AuthService } from '../../core/auth/auth.service';

@Component({
  selector: 'app-user-icon', // RENAMED: UserMenu -> UserIcon
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatDividerModule,
    MatTooltipModule
  ],
  template: `
    <div class="user-icon-container"
         [class.collapsed]="collapsed"
         [class.dark-theme]="themeService.isDark()"
         style="position: absolute !important; bottom: 0 !important; left: 0 !important; right: 0 !important;
                display: flex !important; flex-direction: column !important;
                background: rgba(255, 255, 255, 0.95) !important; border-top: 1px solid rgba(0, 0, 0, 0.08) !important;
                padding: 8px !important; height: auto !important; min-height: 120px !important;">
      <!-- CRITICAL: User Actions ABOVE UserIcon -->
      <div class="user-actions" [class.collapsed]="collapsed"
           style="display: flex !important; flex-direction: column !important; gap: 4px !important;
                  order: 1 !important; flex-shrink: 0 !important; width: 100% !important;
                  align-items: stretch !important; padding: 4px !important; margin-bottom: 8px !important;">
        <!-- Change User Button -->
        <button mat-button
                class="user-action-btn change-user-btn"
                (click)="changeUser()"
                [matTooltip]="collapsed ? 'Change User' : ''"
                matTooltipPosition="right"
                matTooltipClass="sidebar-tooltip"
                aria-label="Change User"
                style="background: rgba(255, 255, 255, 0.8) !important; border: 1px solid rgba(25, 118, 210, 0.1) !important;
                       min-height: 44px !important; border-radius: 6px !important; padding: 8px 12px !important;
                       display: flex !important; align-items: center !important; justify-content: flex-start !important;">
          <mat-icon style="margin-right: 8px !important;">swap_horiz</mat-icon>
          <span *ngIf="!collapsed" class="action-text">Change User</span>
        </button>

        <!-- Sign Out Button -->
        <button mat-button
                class="user-action-btn sign-out-btn"
                (click)="signOut()"
                [matTooltip]="collapsed ? 'Sign Out' : ''"
                matTooltipPosition="right"
                matTooltipClass="sidebar-tooltip"
                aria-label="Sign Out"
                style="background: rgba(255, 255, 255, 0.8) !important; border: 1px solid rgba(25, 118, 210, 0.1) !important;
                       min-height: 44px !important; border-radius: 6px !important; padding: 8px 12px !important;
                       display: flex !important; align-items: center !important; justify-content: flex-start !important;">
          <mat-icon style="margin-right: 8px !important;">logout</mat-icon>
          <span *ngIf="!collapsed" class="action-text">Sign Out</span>
        </button>
      </div>

      <!-- UserIcon Section - BELOW actions, clickable for View Profile -->
      <div class="user-info-section" [class.collapsed]="collapsed"
           style="display: flex !important; order: 2 !important; flex-shrink: 0 !important;
                  width: 100% !important; justify-content: center !important; align-items: center !important;
                  padding: 4px !important; margin-top: 8px !important;">
        <!-- Collapsed state: Only avatar icon - clickable -->
        <button mat-icon-button
                *ngIf="collapsed"
                class="user-avatar-btn"
                (click)="viewProfile()"
                [matTooltip]="'View Profile - ' + userName + ' (' + userRole + ')'"
                matTooltipPosition="right"
                matTooltipClass="sidebar-tooltip"
                aria-label="View Profile">
          <div class="user-avatar">
            <mat-icon>account_circle</mat-icon>
          </div>
        </button>

        <!-- Expanded state: Avatar + name - clickable -->
        <button mat-button
                *ngIf="!collapsed"
                class="user-info-btn"
                (click)="viewProfile()"
                matTooltip="View Profile"
                matTooltipPosition="right"
                matTooltipClass="sidebar-tooltip"
                aria-label="View Profile">
          <div class="user-info">
            <div class="user-avatar">
              <mat-icon>account_circle</mat-icon>
            </div>
            <div class="user-details">
              <span class="user-name">{{ userName }}</span>
              <span class="user-role">{{ userRole }}</span>
            </div>
          </div>
        </button>
      </div>
    </div>
  `,
  styleUrls: ['./user-menu.component.scss']
})
export class UserIconComponent {
  @Input() collapsed = false;
  @Input() userName = 'John Doe';
  @Input() userRole = 'Manager';
  @Input() userEmail = '<EMAIL>';

  private authService = inject(AuthService);
  private router = inject(Router);

  constructor(public themeService: StaffManagerThemeService) {
    // Update user info from auth service
    this.authService.userProfile$.subscribe(profile => {
      if (profile) {
        this.userName = profile.displayName;
        this.userRole = profile.role;
        this.userEmail = profile.email;
      }
    });
  }

  viewProfile() {
    this.authService.userProfile$.subscribe(profile => {
      if (profile?.staffId) {
        // User has a staff profile, navigate directly to it
        this.router.navigate(['/staff/profile', profile.staffId]);
      } else {
        // User needs profile setup, navigate to profile redirect component
        this.router.navigate(['/staff/my-profile']);
      }
    });
  }

  changeUser() {
    // Sign out and redirect to login
    this.authService.signOut().subscribe(() => {
      this.router.navigate(['/auth/login']);
    });
  }

  signOut() {
    this.authService.signOut().subscribe(() => {
      this.router.navigate(['/auth/login']);
    });
  }
}

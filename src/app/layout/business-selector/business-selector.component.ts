import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';

export interface Business {
  id: string;
  name: string;
  logoUrl?: string;
}

@Component({
  selector: 'app-business-selector',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatChipsModule, MatButtonModule, MatTooltipModule, MatDialogModule],
  templateUrl: './business-selector.component.html',
  styleUrl: './business-selector.component.scss'
})
export class BusinessSelectorComponent {
  /** List of all businesses/locations */
  @Input() businesses: Business[] = [];
  /** Array of selected business IDs */
  @Input() selectedBusinessIds: string[] = [];
  /** If sidebar is collapsed */
  @Input() collapsed = false;
  /** Emit when selection changes */
  @Output() selectionChange = new EventEmitter<string[]>();

  constructor(private dialog: MatDialog) {}

  get selectedBusinesses(): Business[] {
    return this.businesses.filter(b => this.selectedBusinessIds.includes(b.id));
  }

  get isOneView(): boolean {
    return this.selectedBusinessIds.length > 1;
  }

  openSelectorDialog() {
    // Dynamic import to avoid circular dependency and SSR issues
    import('./business-selector-dialog/business-selector-dialog.component').then(({ BusinessSelectorDialogComponent }) => {
      const dialogRef = this.dialog.open(BusinessSelectorDialogComponent, {
        data: {
          businesses: this.businesses,
          selectedBusinessIds: this.selectedBusinessIds
        },
        width: '360px',
        autoFocus: true
      });
      dialogRef.afterClosed().subscribe((result: string[] | null) => {
        if (result) {
          this.selectionChange.emit(result);
        }
      });
    });
  }

  /** For accessibility: ARIA label for the selector */
  get ariaLabel(): string {
    return this.isOneView
      ? `OneView active: ${this.selectedBusinesses.map(b => b.name).join(', ')}`
      : `Current business: ${this.selectedBusinesses[0]?.name || 'None'}`;
  }
}

<div class="dialog-header">
  <span>Select businesses</span>
  <button mat-icon-button (click)="cancel()" aria-label="Close dialog">
    <mat-icon>close</mat-icon>
  </button>
</div>
<div class="dialog-search">
  <mat-icon>search</mat-icon>
  <input
    type="text"
    [(ngModel)]="search"
    placeholder="Search businesses"
    aria-label="Search businesses"
  />
</div>
<div class="dialog-actions">
  <button mat-stroked-button (click)="selectAll()" [disabled]="selectedIds.length === data.businesses.length">Select All</button>
  <button mat-stroked-button (click)="clearAll()" [disabled]="selectedIds.length === 0">Clear</button>
  <mat-chip *ngIf="selectedIds.length > 1" color="accent" class="oneview-chip">
    <mat-icon>visibility</mat-icon>
    OneView Active
  </mat-chip>
</div>
<div class="dialog-list">
  <div
    class="dialog-list-item"
    *ngFor="let b of filteredBusinesses"
    (click)="toggleBusiness(b.id)"
    [class.selected]="selectedIds.includes(b.id)"
    tabindex="0"
    (keydown.enter)="toggleBusiness(b.id)"
    (keydown.space)="toggleBusiness(b.id)"
    attr.aria-checked="{{selectedIds.includes(b.id)}}"
    role="checkbox"
  >
    <img *ngIf="b.logoUrl" [src]="b.logoUrl" [alt]="b.name" class="business-logo" />
    <mat-icon *ngIf="!b.logoUrl" class="business-icon">business</mat-icon>
    <span class="business-name">{{ b.name }}</span>
    <mat-checkbox [checked]="selectedIds.includes(b.id)" tabindex="-1"></mat-checkbox>
  </div>
</div>
<div class="dialog-footer">
  <span class="selected-count">{{ selectedIds.length }}/20 selected</span>
  <button mat-flat-button color="primary" (click)="confirm()" [disabled]="selectedIds.length === 0">Confirm</button>
</div>

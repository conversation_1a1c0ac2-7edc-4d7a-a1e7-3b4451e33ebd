import { Component, EventEmitter, Output, Input } from '@angular/core';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatBadgeModule } from '@angular/material/badge';
import { CommonModule, DatePipe } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { fadeInAnimation, pulseAnimation, buttonPressAnimation } from '../core/animations/staffmanager-animations';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule, MatToolbarModule, MatIconModule, MatButtonModule, MatBadgeModule, MatMenuModule, MatDividerModule],
  providers: [DatePipe],
  animations: [fadeInAnimation, pulseAnimation, buttonPressAnimation],
  template: `
    <mat-toolbar class="app-header" [@fadeIn]
                 style="background: linear-gradient(135deg, #1976d2 0%, #42a5f5 50%, #7c3aed 100%);
                        color: white;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                        height: 64px;
                        position: relative;
                        z-index: 1000;">
      <button mat-icon-button
              *ngIf="showMenuButton"
              (click)="menuToggle.emit()"
              aria-label="Open sidebar"
              style="color: white; margin-right: 16px; width: 44px; height: 44px; border-radius: 8px; transition: all 0.2s ease;"
              [@buttonPress]>
        <mat-icon>menu</mat-icon>
      </button>

      <div [@fadeIn] style="display: flex; align-items: center; margin-left: 16px; padding: 8px 16px;
                           border-radius: 20px; background: rgba(255,255,255,0.2);
                           backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.3);
                           transition: all 0.3s ease; cursor: pointer;">
        <span style="font-size: 0.9rem; font-weight: 500; margin-right: 8px; opacity: 0.9;">{{ today | date:'MMMM d, yyyy' }}</span>
        <span style="font-size: 0.9rem; font-weight: 600; color: rgba(255,255,255,0.95);">{{ now | date:'h:mm a' }}</span>
      </div>

      <span [@fadeIn] style="position: absolute; left: 50%; transform: translateX(-50%);
                            font-weight: 700; font-size: 1.5rem; letter-spacing: -0.5px;
                            text-shadow: 0 2px 4px rgba(0,0,0,0.3);">StaffManager</span>
      <span style="flex: 1;"></span>

      <button mat-icon-button
              [matMenuTriggerFor]="notificationsMenu"
              aria-label="Notifications"
              style="color: white; margin: 0 4px; width: 44px; height: 44px; border-radius: 8px;
                     transition: all 0.2s ease; background: rgba(255,255,255,0.1);"
              [@buttonPress]
              [class.pulse]="hasNewNotifications">
        <mat-icon [matBadge]="notificationCount"
                  [matBadgeHidden]="notificationCount === 0"
                  matBadgeColor="warn"
                  style="color: white;"
                  aria-hidden="false"
                  role="img"
                  [attr.aria-label]="notificationCount + ' new notifications'">
          notifications
        </mat-icon>
      </button>
      <mat-menu #notificationsMenu="matMenu">
        <button mat-menu-item *ngFor="let n of notifications">
          <mat-icon color="primary">notifications</mat-icon>
          <span>{{ n }}</span>
        </button>
        <mat-divider></mat-divider>
        <button mat-menu-item>View all notifications</button>
      </mat-menu>
      <button mat-icon-button [matMenuTriggerFor]="chatMenu" aria-label="Chat"
              style="color: white; margin: 0 4px; width: 44px; height: 44px; border-radius: 8px;
                     transition: all 0.2s ease; background: rgba(255,255,255,0.1);">
        <mat-icon matBadge="2" matBadgeColor="warn" style="color: white;" aria-hidden="false" role="img" aria-label="2 unread chat messages">chat</mat-icon>
      </button>
      <mat-menu #chatMenu="matMenu">
        <button mat-menu-item *ngFor="let c of chats">
          <mat-icon color="primary">chat</mat-icon>
          <span>{{ c }}</span>
        </button>
        <mat-divider></mat-divider>
        <button mat-menu-item>Open chat</button>
      </mat-menu>
    </mat-toolbar>
  `,
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent {
  @Input() showMenuButton = false;
  @Output() menuToggle = new EventEmitter<void>();

  today = new Date();
  now = new Date();

  notifications = [
    'New staff member added',
    'Meeting scheduled for tomorrow',
    'Payroll exported to ADP'
  ];

  chats = [
    '2 unread messages',
    'Support: New reply'
  ];

  // Computed properties for enhanced functionality
  get notificationCount(): number {
    return this.notifications.length;
  }

  get hasNewNotifications(): boolean {
    return this.notificationCount > 0;
  }

  get chatCount(): number {
    return this.chats.length;
  }

  constructor() {
    // Update time every minute
    setInterval(() => {
      this.now = new Date();
    }, 60000);
  }
}
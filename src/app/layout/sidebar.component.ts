import { Component, Input, Output, EventEmitter } from '@angular/core';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatDividerModule } from '@angular/material/divider';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTooltipModule } from '@angular/material/tooltip';
import { BusinessSelectorComponent, Business } from './business-selector/business-selector.component';
import { UserIconComponent } from './user-menu/user-menu.component';
import { StaffManagerThemeService } from '../core/theme/staffmanager-theme';
import { sidebarToggleAnimation, fadeInAnimation, staggerAnimation, buttonPressAnimation, expandCollapseAnimation } from '../core/animations/staffmanager-animations';
import { IconService } from '../core/services/icon.service';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule, MatListModule, MatIconModule, MatButtonModule, MatDividerModule, MatSelectModule, MatOptionModule, MatBadgeModule, MatTooltipModule, BusinessSelectorComponent, UserIconComponent],
  animations: [sidebarToggleAnimation, fadeInAnimation, staggerAnimation, buttonPressAnimation, expandCollapseAnimation],
  host: {
    '[class.collapsed]': 'collapsed'
  },
  template: `
    <nav [style.width]="collapsed ? '64px' : '240px'"
         [style.background]="'linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%)'"
         [style.height]="'100vh'"
         [style.display]="'flex'"
         [style.flex-direction]="'column'"
         [style.box-shadow]="'2px 0 8px rgba(0,0,0,0.1)'"
         [style.border-right]="'1px solid #e0e0e0'"
         [style.transition]="'all 0.3s ease'"
         [style.overflow]="'hidden'"
         [@fadeIn]
         aria-label="Main navigation">

      <!-- Hamburger Toggle Button -->
      <div [style.padding]="'12px 8px'"
           [style.border-bottom]="'1px solid #e0e0e0'"
           [style.background]="'#ffffff'"
           [style.display]="'flex'"
           [style.justify-content]="collapsed ? 'center' : 'flex-start'">
        <button mat-icon-button
                (click)="onToggleSidebar()"
                [style.width]="'44px'"
                [style.height]="'44px'"
                [style.background]="'#f5f5f5'"
                [style.border]="'1px solid #e0e0e0'"
                [style.border-radius]="'6px'"
                [style.transition]="'all 0.2s ease'"
                [attr.aria-label]="collapsed ? 'Expand sidebar' : 'Collapse sidebar'">
          <div [style.width]="'20px'"
               [style.height]="'16px'"
               [style.display]="'flex'"
               [style.flex-direction]="'column'"
               [style.justify-content]="'space-between'">
            <span [style.width]="'100%'"
                  [style.height]="'3px'"
                  [style.background]="'#333333'"
                  [style.border-radius]="'1px'"></span>
            <span [style.width]="'100%'"
                  [style.height]="'3px'"
                  [style.background]="'#333333'"
                  [style.border-radius]="'1px'"></span>
            <span [style.width]="'100%'"
                  [style.height]="'3px'"
                  [style.background]="'#333333'"
                  [style.border-radius]="'1px'"></span>
          </div>
        </button>
      </div>

      <!-- Business Selector Section -->
      <div [style.padding]="'16px 8px'"
           [style.border-bottom]="'1px solid #e0e0e0'"
           [style.background]="'#ffffff'"
           [style.display]="'flex'"
           [style.justify-content]="collapsed ? 'center' : 'flex-start'">
        <app-business-selector
          [businesses]="businesses"
          [selectedBusinessIds]="selectedBusinessIds"
          [collapsed]="collapsed"
          (selectionChange)="onBusinessSelectionChange($event)"
        ></app-business-selector>
      </div>

      <!-- Navigation List -->
      <div [style.flex]="'1'"
           [style.overflow-y]="'auto'"
           [style.padding]="'16px 0'">

        <div *ngIf="!collapsed"
             [style.font-size]="'1rem'"
             [style.font-weight]="'700'"
             [style.color]="'#1976d2'"
             [style.margin]="'16px 0 6px 24px'"
             [style.letter-spacing]="'0.04em'">Main</div>

        <a routerLink="/dashboard"
           routerLinkActive="active"
           [style.display]="'flex'"
           [style.align-items]="'center'"
           [style.padding]="'8px 12px'"
           [style.margin]="'2px 8px'"
           [style.border-radius]="'8px'"
           [style.text-decoration]="'none'"
           [style.color]="'#333333'"
           [style.font-weight]="'500'"
           [style.min-height]="'48px'"
           [style.transition]="'all 0.2s ease'"
           [style.cursor]="'pointer'"
           aria-label="Dashboard">
          <mat-icon [style.margin-right]="collapsed ? '0' : '18px'"
                    [style.color]="'#666666'"
                    [style.font-size]="collapsed ? '2rem' : '1.6rem'"
                    [style.width]="collapsed ? '2rem' : '1.6rem'"
                    [style.height]="collapsed ? '2rem' : '1.6rem'">dashboard</mat-icon>
          <span *ngIf="!collapsed">Dashboard</span>
        </a>
        <a routerLink="/calendar"
           routerLinkActive="active"
           [style.display]="'flex'"
           [style.align-items]="'center'"
           [style.padding]="'8px 12px'"
           [style.margin]="'2px 8px'"
           [style.border-radius]="'8px'"
           [style.text-decoration]="'none'"
           [style.color]="'#333333'"
           [style.font-weight]="'500'"
           [style.min-height]="'48px'"
           [style.transition]="'all 0.2s ease'"
           [style.cursor]="'pointer'"
           aria-label="Calendar">
          <mat-icon [style.margin-right]="collapsed ? '0' : '18px'"
                    [style.color]="'#666666'"
                    [style.font-size]="collapsed ? '2rem' : '1.6rem'"
                    [style.width]="collapsed ? '2rem' : '1.6rem'"
                    [style.height]="collapsed ? '2rem' : '1.6rem'">calendar_month</mat-icon>
          <span *ngIf="!collapsed">Calendar</span>
        </a>
        <a routerLink="/staff"
           routerLinkActive="active"
           [style.display]="'flex'"
           [style.align-items]="'center'"
           [style.padding]="'8px 12px'"
           [style.margin]="'2px 8px'"
           [style.border-radius]="'8px'"
           [style.text-decoration]="'none'"
           [style.color]="'#333333'"
           [style.font-weight]="'500'"
           [style.min-height]="'48px'"
           [style.transition]="'all 0.2s ease'"
           [style.cursor]="'pointer'"
           aria-label="Staff">
          <mat-icon [style.margin-right]="collapsed ? '0' : '18px'"
                    [style.color]="'#666666'"
                    [style.font-size]="collapsed ? '2rem' : '1.6rem'"
                    [style.width]="collapsed ? '2rem' : '1.6rem'"
                    [style.height]="collapsed ? '2rem' : '1.6rem'">people</mat-icon>
          <span *ngIf="!collapsed">Staff</span>
        </a>
        <a routerLink="/tasks"
           routerLinkActive="active"
           [style.display]="'flex'"
           [style.align-items]="'center'"
           [style.padding]="'8px 12px'"
           [style.margin]="'2px 8px'"
           [style.border-radius]="'8px'"
           [style.text-decoration]="'none'"
           [style.color]="'#333333'"
           [style.font-weight]="'500'"
           [style.min-height]="'48px'"
           [style.transition]="'all 0.2s ease'"
           [style.cursor]="'pointer'"
           aria-label="Tasks">
          <mat-icon [style.margin-right]="collapsed ? '0' : '18px'"
                    [style.color]="'#666666'"
                    [style.font-size]="collapsed ? '2rem' : '1.6rem'"
                    [style.width]="collapsed ? '2rem' : '1.6rem'"
                    [style.height]="collapsed ? '2rem' : '1.6rem'">assignment_turned_in</mat-icon>
          <span *ngIf="!collapsed">Tasks</span>
        </a>

        <a routerLink="/settings"
           routerLinkActive="active"
           [style.display]="'flex'"
           [style.align-items]="'center'"
           [style.padding]="'8px 12px'"
           [style.margin]="'2px 8px'"
           [style.border-radius]="'8px'"
           [style.text-decoration]="'none'"
           [style.color]="'#333333'"
           [style.font-weight]="'500'"
           [style.min-height]="'48px'"
           [style.transition]="'all 0.2s ease'"
           [style.cursor]="'pointer'"
           aria-label="Settings">
          <mat-icon [style.margin-right]="collapsed ? '0' : '18px'"
                    [style.color]="'#666666'"
                    [style.font-size]="collapsed ? '2rem' : '1.6rem'"
                    [style.width]="collapsed ? '2rem' : '1.6rem'"
                    [style.height]="collapsed ? '2rem' : '1.6rem'">settings</mat-icon>
          <span *ngIf="!collapsed">Settings</span>
        </a>
      </div>

      <!-- User Icon at Bottom -->
      <app-user-icon [collapsed]="collapsed"></app-user-icon>
    </nav>
  `,
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent {
  @Input() collapsed = false;

  @Output() businessSelectionChange = new EventEmitter<string[]>();
  @Output() toggleSidebar = new EventEmitter<void>();

  staffMenuOpen = false;
  selectedBusinessIds: string[] = ['1'];
  businesses: Business[] = [
    { id: '1', name: 'S&E Jewelers', logoUrl: '' },
    { id: '2', name: 'Diamond District', logoUrl: '' },
    { id: '3', name: 'Gold Rush', logoUrl: '' },
    { id: '4', name: 'Precious Gems Co.', logoUrl: '' }
  ];
  taskBadgeCount = 5;
  goalBadgeCount = 3;
  staffHubStatus: 'online' | 'offline' | 'unknown' = 'unknown';

  constructor(
    public themeService: StaffManagerThemeService,
    private iconService: IconService
  ) {}

  get navigationItems() {
    return [
      { route: '/dashboard', icon: 'dashboard', label: 'Dashboard' },
      { route: '/calendar', icon: 'calendar', label: 'Calendar' },
      { route: '/staff', icon: 'staff', label: 'Staff' },
      { route: '/tasks', icon: 'tasks', label: 'Tasks' },
      { route: '/goals', icon: 'goals', label: 'Goals' },
      { route: '/time', icon: 'time', label: 'Time' },
      { route: '/settings', icon: 'settings', label: 'Settings' }
    ];
  }

  getIcon(semanticName: string): string {
    return this.iconService.getIconName(semanticName);
  }

  toggleStaffMenu() {
    this.staffMenuOpen = !this.staffMenuOpen;
  }

  onToggleSidebar() {
    console.log('🔥 HAMBURGER CLICKED - TOGGLE SIDEBAR');
    this.toggleSidebar.emit();
  }

  onBusinessSelectionChange(ids: string[]) {
    this.selectedBusinessIds = ids;
    this.businessSelectionChange.emit(ids);
  }
}
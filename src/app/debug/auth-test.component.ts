import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';

import { FormsModule } from '@angular/forms';
import { AuthService } from '../core/auth/auth.service';
import { Auth, user } from '@angular/fire/auth';
import { Firestore } from '@angular/fire/firestore';
import { FirebaseContextService } from '../core/services/firebase-context.service';
import { take } from 'rxjs';

@Component({
  selector: 'app-auth-test',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    ButtonModule,
    InputTextModule
    FormsModule
  ],
  template: `
    <div style="padding: 20px; max-width: 600px; margin: 0 auto;">
      <p-card>
        <p-card-header>
          <p-card-title>🔧 Authentication Debug Test</ng-template>
        </ng-template>
        <p-card-content>

          <!-- Firebase Connection Status -->
          <div style="margin-bottom: 20px;">
            <h3>Firebase Connection Status</h3>
            <p><strong>Auth Instance:</strong> {{ authInstance ? '✅ Connected' : '❌ Not Connected' }}</p>
            <p><strong>Firestore Instance:</strong> {{ firestoreInstance ? '✅ Connected' : '❌ Not Connected' }}</p>
            <p><strong>Environment:</strong> {{ environment }}</p>
          </div>

          <!-- Current User Status -->
          <div style="margin-bottom: 20px;">
            <h3>Current User Status</h3>
            <p><strong>User Observable:</strong> {{ currentUser ? '✅ User Found' : '❌ No User' }}</p>
            <div *ngIf="currentUser" style="background: #e8f5e8; padding: 10px; border-radius: 4px;">
              <p><strong>UID:</strong> {{ currentUser.uid }}</p>
              <p><strong>Email:</strong> {{ currentUser.email }}</p>
              <p><strong>Display Name:</strong> {{ currentUser.displayName }}</p>
              <p><strong>Email Verified:</strong> {{ currentUser.emailVerified }}</p>
            </div>
            <div *ngIf="!currentUser" style="background: #ffe8e8; padding: 10px; border-radius: 4px;">
              <p>No authenticated user found</p>
            </div>
          </div>

          <!-- User Profile Status -->
          <div style="margin-bottom: 20px;">
            <h3>User Profile Status</h3>
            <p><strong>Profile Observable:</strong> {{ userProfile ? '✅ Profile Found' : '❌ No Profile' }}</p>
            <div *ngIf="userProfile" style="background: #e8f5e8; padding: 10px; border-radius: 4px;">
              <p><strong>Role:</strong> {{ userProfile.role }}</p>
              <p><strong>Staff ID:</strong> {{ userProfile.staffId || 'Not set' }}</p>
              <p><strong>Business IDs:</strong> {{ userProfile.businessIds?.join(', ') || 'None' }}</p>
            </div>
          </div>

          <!-- Test Login Form -->
          <div style="margin-bottom: 20px;">
            <h3>Test Login</h3>
            <div class="p-field" appearance="outline" style="width: 100%; margin-bottom: 10px;">
              <label>Email</label>
              <input matInput [(ngModel)]="testEmail" type="email">
            </div>
            <div class="p-field" appearance="outline" style="width: 100%; margin-bottom: 10px;">
              <label>Password</label>
              <input matInput [(ngModel)]="testPassword" type="password">
            </div>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
              <button p-button color="primary" (click)="testRegistration()" [disabled]="loading">
                {{ loading ? 'Testing...' : 'Test Registration' }}
              </button>
              <button p-button color="primary" (click)="testLogin()" [disabled]="loading">
                {{ loading ? 'Testing...' : 'Test Login' }}
              </button>
              <button p-button color="accent" (click)="testGoogleLogin()" [disabled]="loading">
                Test Google Login
              </button>
              <button p-button color="warn" (click)="fixUserProfile()" [disabled]="loading">
                Fix Missing Profile
              </button>
              <button p-button (click)="testLogout()" [disabled]="loading">
                Test Logout
              </button>
            </div>
          </div>

          <!-- Test Results -->
          <div *ngIf="testResults.length > 0">
            <h3>Test Results</h3>
            <div style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;">
              <div *ngFor="let result of testResults"
                   [style.color]="result.type === 'error' ? 'red' : result.type === 'success' ? 'green' : 'blue'">
                <strong>{{ result.timestamp }}:</strong> {{ result.message }}
              </div>
            </div>
            <button mat-button (click)="clearResults()" style="margin-top: 10px;">Clear Results</button>
          </div>

        </ng-template>
      </p-card>
    </div>
  `
})
export class AuthTestComponent implements OnInit {
  private authService = inject(AuthService);
  private auth = inject(Auth);
  private firestore = inject(Firestore);
  private firebaseContext = inject(FirebaseContextService);

  authInstance: any = null;
  firestoreInstance: any = null;
  environment = 'development';
  currentUser: any = null;
  userProfile: any = null;

  testEmail = '<EMAIL>';
  testPassword = 'password123';
  loading = false;

  testResults: Array<{type: string, message: string, timestamp: string}> = [];

  ngOnInit() {
    this.checkFirebaseInstances();
    this.subscribeToAuth();
    this.addResult('info', 'Auth test component initialized');
  }

  private checkFirebaseInstances() {
    this.authInstance = this.auth;
    this.firestoreInstance = this.firestore;

    this.addResult('info', `Auth instance: ${this.authInstance ? 'Connected' : 'Not Connected'}`);
    this.addResult('info', `Firestore instance: ${this.firestoreInstance ? 'Connected' : 'Not Connected'}`);
  }

  private subscribeToAuth() {
    // Subscribe to auth state changes
    this.authService.user$.subscribe(user => {
      this.currentUser = user;
      this.addResult('info', `Auth state changed: ${user ? `User ${user.email}` : 'No user'}`);

      // Debug: Check if user profile exists when user changes
      if (user) {
        this.checkUserProfileExists(user.uid);
      }
    });

    // Subscribe to user profile changes
    this.authService.userProfile$.subscribe(profile => {
      this.userProfile = profile;
      this.addResult('info', `Profile state changed: ${profile ? `Profile for ${profile.email}` : 'No profile'}`);

      // Debug: Log profile details
      if (profile) {
        this.addResult('info', `Profile details: Role=${profile.role}, UID=${profile.uid}`);
      } else {
        this.addResult('error', 'No user profile found - this may cause issues');
      }
    });
  }

  private checkUserProfileExists(uid: string) {
    // Check if user profile document exists in Firestore using FirebaseContextService
    this.firebaseContext.getDocument(`users/${uid}`).subscribe({
      next: (docSnap) => {
        if (docSnap.exists()) {
          this.addResult('success', `User profile document exists in Firestore`);
          const data = docSnap.data();
          this.addResult('info', `Profile data: ${JSON.stringify(data, null, 2)}`);
        } else {
          this.addResult('error', `User profile document NOT found in Firestore for UID: ${uid}`);
          this.addResult('info', 'This explains the "No user profile found" error');
        }
      },
      error: (error) => {
        this.addResult('error', `Error checking user profile: ${error.message}`);
      }
    });
  }

  async testLogin() {
    this.loading = true;
    this.addResult('info', `Attempting login with ${this.testEmail}`);

    try {
      this.authService.signInWithEmail(this.testEmail, this.testPassword).subscribe({
        next: (profile) => {
          this.addResult('success', `Login successful: ${profile?.email || 'Unknown user'}`);
          this.loading = false;
        },
        error: (error) => {
          this.addResult('error', `Login failed: ${error.message} (Code: ${error.code})`);
          this.loading = false;
        }
      });
    } catch (error: any) {
      this.addResult('error', `Login error: ${error.message}`);
      this.loading = false;
    }
  }

  async testRegistration() {
    this.loading = true;
    this.addResult('info', `Attempting registration with ${this.testEmail}`);

    try {
      this.authService.signUpWithEmail(this.testEmail, this.testPassword, 'Test User', 'admin').subscribe({
        next: (profile) => {
          this.addResult('success', `Registration successful: ${profile?.email || 'Unknown user'}`);
          this.loading = false;
        },
        error: (error) => {
          this.addResult('error', `Registration failed: ${error.message} (Code: ${error.code})`);
          this.loading = false;
        }
      });
    } catch (error: any) {
      this.addResult('error', `Registration error: ${error.message}`);
      this.loading = false;
    }
  }

  async testGoogleLogin() {
    this.loading = true;
    this.addResult('info', 'Attempting Google login');

    try {
      this.authService.signInWithGoogle().subscribe({
        next: (profile) => {
          this.addResult('success', `Google login successful: ${profile?.email || 'Unknown user'}`);
          this.loading = false;
        },
        error: (error) => {
          this.addResult('error', `Google login failed: ${error.message}`);
          this.loading = false;
        }
      });
    } catch (error: any) {
      this.addResult('error', `Google login error: ${error.message}`);
      this.loading = false;
    }
  }

  async fixUserProfile() {
    this.loading = true;
    this.addResult('info', 'Attempting to fix missing user profile');

    // Get current user
    this.authService.user$.pipe(take(1)).subscribe(user => {
      if (user) {
        this.addResult('info', `Creating profile for user: ${user.email}`);

        // Create user profile manually using FirebaseContextService
        const userProfile = {
          uid: user.uid,
          email: user.email!,
          displayName: user.displayName || 'User',
          role: 'admin', // Default to admin for testing
          businessIds: [],
          primaryBusinessId: '',
          createdAt: new Date(),
          lastLoginAt: new Date()
        };

        this.firebaseContext.setDocument(`users/${user.uid}`, userProfile).subscribe({
          next: () => {
            this.addResult('success', 'User profile created successfully!');
            this.addResult('info', 'Try refreshing the page to see the profile load');
            this.loading = false;
          },
          error: (error) => {
            this.addResult('error', `Error creating profile: ${error.message}`);
            this.loading = false;
          }
        });
      } else {
        this.addResult('error', 'No authenticated user found');
        this.loading = false;
      }
    });
  }

  async testLogout() {
    this.loading = true;
    this.addResult('info', 'Attempting logout');

    try {
      this.authService.signOut().subscribe({
        next: () => {
          this.addResult('success', 'Logout successful');
          this.loading = false;
        },
        error: (error) => {
          this.addResult('error', `Logout failed: ${error.message}`);
          this.loading = false;
        }
      });
    } catch (error: any) {
      this.addResult('error', `Logout error: ${error.message}`);
      this.loading = false;
    }
  }

  private addResult(type: string, message: string) {
    const timestamp = new Date().toLocaleTimeString();
    this.testResults.unshift({ type, message, timestamp });
    console.log(`🔧 Auth Test [${type.toUpperCase()}]:`, message);
  }

  clearResults() {
    this.testResults = [];
  }
}

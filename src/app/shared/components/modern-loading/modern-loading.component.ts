import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { fadeInAnimation, pulseAnimation, skeletonAnimation } from '../../../core/animations/staffmanager-animations';

@Component({
  selector: 'app-modern-loading',
  standalone: true,
  imports: [CommonModule, MatProgressSpinnerModule, MatIconModule],
  animations: [fadeInAnimation, pulseAnimation, skeletonAnimation],
  template: `
    <div class="modern-loading-container" 
         [ngClass]="'loading-' + variant"
         [@fadeIn]>
      
      <!-- Spinner Variant -->
      <div *ngIf="variant === 'spinner'" class="loading-spinner" [@pulseAnimation]="pulseState">
        <mat-spinner [diameter]="size" [strokeWidth]="strokeWidth"></mat-spinner>
        <p *ngIf="message" class="loading-message">{{ message }}</p>
      </div>
      
      <!-- Dots Variant -->
      <div *ngIf="variant === 'dots'" class="loading-dots">
        <div class="dot" [style.animation-delay]="'0ms'"></div>
        <div class="dot" [style.animation-delay]="'150ms'"></div>
        <div class="dot" [style.animation-delay]="'300ms'"></div>
        <p *ngIf="message" class="loading-message">{{ message }}</p>
      </div>
      
      <!-- Skeleton Variant -->
      <div *ngIf="variant === 'skeleton'" class="loading-skeleton" [@skeletonAnimation]="skeletonState">
        <div class="skeleton-line" [style.width]="'100%'"></div>
        <div class="skeleton-line" [style.width]="'80%'"></div>
        <div class="skeleton-line" [style.width]="'60%'"></div>
      </div>
      
      <!-- Pulse Variant -->
      <div *ngIf="variant === 'pulse'" class="loading-pulse" [@pulseAnimation]="pulseState">
        <div class="pulse-circle"></div>
        <p *ngIf="message" class="loading-message">{{ message }}</p>
      </div>
      
      <!-- Wave Variant -->
      <div *ngIf="variant === 'wave'" class="loading-wave">
        <div class="wave-bar" [style.animation-delay]="'0ms'"></div>
        <div class="wave-bar" [style.animation-delay]="'100ms'"></div>
        <div class="wave-bar" [style.animation-delay]="'200ms'"></div>
        <div class="wave-bar" [style.animation-delay]="'300ms'"></div>
        <div class="wave-bar" [style.animation-delay]="'400ms'"></div>
        <p *ngIf="message" class="loading-message">{{ message }}</p>
      </div>
      
    </div>
  `,
  styleUrls: ['./modern-loading.component.scss']
})
export class ModernLoadingComponent {
  @Input() variant: 'spinner' | 'dots' | 'skeleton' | 'pulse' | 'wave' = 'spinner';
  @Input() size: number = 40;
  @Input() strokeWidth: number = 4;
  @Input() message?: string;
  @Input() color: 'primary' | 'accent' | 'warn' = 'primary';

  pulseState: 'normal' | 'pulsed' = 'normal';
  skeletonState: 'loading' | 'loaded' = 'loading';

  constructor() {
    // Animate pulse state
    setInterval(() => {
      this.pulseState = this.pulseState === 'normal' ? 'pulsed' : 'normal';
    }, 1000);

    // Animate skeleton state
    setInterval(() => {
      this.skeletonState = this.skeletonState === 'loading' ? 'loaded' : 'loading';
    }, 1500);
  }
}

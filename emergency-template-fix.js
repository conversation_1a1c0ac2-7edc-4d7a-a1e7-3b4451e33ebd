#!/usr/bin/env node

/**
 * Emergency fix for critical template syntax errors
 */

const fs = require('fs');
const path = require('path');

function emergencyFix(filePath) {
  console.log(`Emergency fixing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Fix critical template syntax errors
  const criticalFixes = [
    // Fix broken closing tags
    {
      from: /<\/div<\/ng-template>/g,
      to: '</div></ng-template>'
    },
    {
      from: /<\/p-button<\/ng-template>/g,
      to: '</p-button></ng-template>'
    },
    {
      from: /<\/span<\/ng-template>/g,
      to: '</span></ng-template>'
    },
    {
      from: /<\/p<\/ng-template>/g,
      to: '</p></ng-template>'
    },
    {
      from: /<\/ng-template<\/ng-template>/g,
      to: '</ng-template>'
    },
    // Fix broken opening tags
    {
      from: /<h3<\/ng-template>/g,
      to: '<h3>Header</h3></ng-template>'
    },
    {
      from: /<p<\/ng-template>/g,
      to: '<p>Content</p></ng-template>'
    },
    // Fix import syntax errors
    {
      from: /FormsModule\s+ButtonModule/g,
      to: 'FormsModule, ButtonModule'
    },
    {
      from: /ButtonModule\s+InputTextModule/g,
      to: 'ButtonModule, InputTextModule'
    },
    {
      from: /TableModule,\s+DropdownModule/g,
      to: 'TableModule, DropdownModule'
    },
    {
      from: /ToolbarModule,\s+ToolbarModule/g,
      to: 'ToolbarModule'
    },
    // Fix Material Dialog references
    {
      from: /MatDialogRef/g,
      to: 'DynamicDialogRef'
    },
    {
      from: /MAT_DIALOG_DATA/g,
      to: 'DynamicDialogConfig'
    },
    {
      from: /MatSnackBar/g,
      to: 'MessageService'
    }
  ];

  criticalFixes.forEach(fix => {
    if (content.match(fix.from)) {
      content = content.replace(fix.from, fix.to);
      modified = true;
    }
  });

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Emergency fixed: ${filePath}`);
    return true;
  }
  
  return false;
}

function findFiles(dir, extensions = ['.ts', '.html']) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.includes('node_modules') && !item.includes('.git')) {
        traverse(fullPath);
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Main execution
function main() {
  console.log('🚨 Emergency template syntax fixes...\n');
  
  const srcDir = path.join(process.cwd(), 'src');
  const files = findFiles(srcDir);
  
  let fixedCount = 0;
  
  files.forEach(file => {
    if (emergencyFix(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n🚨 Emergency fixes complete!`);
  console.log(`📊 Files processed: ${files.length}`);
  console.log(`✅ Files fixed: ${fixedCount}`);
}

if (require.main === module) {
  main();
}

module.exports = { emergencyFix };
